import { forwardRef, useState } from 'react';
import { Search, X } from 'lucide-react';
import { Input } from '@/components/atoms';
import { Button } from '@/components/atoms';
import { Icon } from '@/components/atoms';
import { cn } from '@/lib/utils';

export interface SearchBoxProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onSearch?: (value: string) => void;
  onClear?: () => void;
  className?: string;
  disabled?: boolean;
  loading?: boolean;
  size?: 'sm' | 'md' | 'lg';
  showClearButton?: boolean;
  autoFocus?: boolean;
}

const SearchBox = forwardRef<HTMLInputElement, SearchBoxProps>(
  ({
    placeholder = 'Search...',
    value = '',
    onChange,
    onSearch,
    onClear,
    className,
    disabled = false,
    loading = false,
    size = 'md',
    showClearButton = true,
    autoFocus = false,
  }, ref) => {
    const [internalValue, setInternalValue] = useState(value);
    const currentValue = value !== undefined ? value : internalValue;

    const handleChange = (newValue: string) => {
      if (value === undefined) {
        setInternalValue(newValue);
      }
      onChange?.(newValue);
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        onSearch?.(currentValue);
      }
      if (e.key === 'Escape') {
        handleClear();
      }
    };

    const handleClear = () => {
      const newValue = '';
      if (value === undefined) {
        setInternalValue(newValue);
      }
      onChange?.(newValue);
      onClear?.();
    };

    const sizeClasses = {
      sm: 'h-8',
      md: 'h-10',
      lg: 'h-12',
    };

    return (
      <div className={cn('relative flex items-center', className)}>
        {/* Search icon */}
        <div className="absolute left-3 z-10">
          <Icon
            icon={Search}
            size={size === 'sm' ? 'sm' : 'md'}
            variant="muted"
          />
        </div>

        {/* Input field */}
        <Input
          ref={ref}
          type="text"
          placeholder={placeholder}
          value={currentValue}
          onChange={(e) => handleChange(e.target.value)}
          onKeyDown={handleKeyDown}
          disabled={disabled || loading}
          autoFocus={autoFocus}
          className={cn(
            'pl-10',
            showClearButton && currentValue && 'pr-10',
            sizeClasses[size]
          )}
        />

        {/* Clear button */}
        {showClearButton && currentValue && !loading && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleClear}
            disabled={disabled}
            className="absolute right-1 h-8 w-8 p-0 hover:bg-transparent"
          >
            <Icon
              icon={X}
              size="sm"
              variant="muted"
            />
            <span className="sr-only">Clear search</span>
          </Button>
        )}

        {/* Loading indicator */}
        {loading && (
          <div className="absolute right-3">
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-mystical-600 border-t-transparent" />
          </div>
        )}
      </div>
    );
  }
);

SearchBox.displayName = 'SearchBox';

export { SearchBox };
