'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/atoms';
import { Button } from '@/components/atoms';
import { Typography } from '@/components/atoms';
import { Badge } from '@/components/atoms';
import { Progress } from '@/components/atoms';
import { 
  Share2, 
  Download, 
  RefreshCw, 
  Star,
  Sparkles,
  Heart,
  TrendingUp,
  Copy,
  Check,
  ExternalLink
} from 'lucide-react';
import { Locale } from '@/types';
import { getMultilingualLayoutClasses, getCulturalColors } from '@/lib/multilingual-layout';

interface TestResult {
  summary: string;
  details: string[];
  recommendations: string[];
  aiInsights: string;
  shareableContent: {
    title: string;
    description: string;
    image?: string;
  };
  metadata: {
    aiModel: string;
    aiProvider: string;
    generatedAt: string;
    confidence: number;
  };
}

interface TestResultProps {
  result: TestResult;
  testInfo: {
    id: string;
    name: Record<string, string>;
    type: string;
    difficulty: string;
  };
  shareToken: string;
  locale: Locale;
  onRetake: () => void;
  onNewTest: () => void;
}

export function TestResult({
  result,
  testInfo,
  shareToken,
  locale,
  onRetake,
  onNewTest,
}: TestResultProps) {
  const [copied, setCopied] = useState(false);
  const [isSharing, setIsSharing] = useState(false);

  const layoutClasses = getMultilingualLayoutClasses(locale);
  const culturalColors = getCulturalColors(locale);

  const shareUrl = `${window.location.origin}/tests/results/${shareToken}`;

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  const handleShare = async () => {
    setIsSharing(true);
    
    try {
      if (navigator.share) {
        await navigator.share({
          title: result.shareableContent.title,
          text: result.shareableContent.description,
          url: shareUrl,
        });
      } else {
        // 回退到复制链接
        await handleCopyLink();
      }
    } catch (error) {
      console.error('Failed to share:', error);
    } finally {
      setIsSharing(false);
    }
  };

  const handleDownloadPDF = () => {
    // 实现PDF下载功能
    console.log('Download PDF functionality to be implemented');
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getTypeEmoji = (type: string) => {
    switch (type.toLowerCase()) {
      case 'tarot':
        return '🔮';
      case 'astrology':
        return '⭐';
      case 'numerology':
        return '🔢';
      case 'personality':
        return '🧠';
      case 'compatibility':
        return '💕';
      default:
        return '✨';
    }
  };

  return (
    <div className={`max-w-4xl mx-auto p-6 space-y-6 ${layoutClasses}`}>
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="text-6xl">{getTypeEmoji(testInfo.type)}</div>
        
        <Typography variant="h1" locale={locale} className="text-center">
          Your {testInfo.name[locale] || testInfo.name.en} Results
        </Typography>
        
        <div className="flex items-center justify-center space-x-2">
          <Badge className={culturalColors.primary}>
            {testInfo.type}
          </Badge>
          <Badge variant="outline">
            {testInfo.difficulty}
          </Badge>
          <Badge 
            variant="outline" 
            className={getConfidenceColor(result.metadata.confidence)}
          >
            {Math.round(result.metadata.confidence * 100)}% Confidence
          </Badge>
        </div>
      </div>

      {/* Main Result */}
      <Card className="bg-gradient-to-br from-mystical-50 to-gold-50 dark:from-mystical-900/20 dark:to-gold-900/20 border-mystical-200">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Sparkles className="w-5 h-5 text-mystical-600" />
            <span>Your Mystical Insights</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Typography variant="lead" locale={locale} className="text-center mb-6">
            {result.summary}
          </Typography>
          
          {/* AI Confidence Indicator */}
          <div className="flex items-center justify-center space-x-2 mb-4">
            <span className="text-sm text-muted-foreground">AI Analysis Confidence:</span>
            <Progress value={result.metadata.confidence * 100} className="w-32 h-2" />
            <span className={`text-sm font-medium ${getConfidenceColor(result.metadata.confidence)}`}>
              {Math.round(result.metadata.confidence * 100)}%
            </span>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Analysis */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Star className="w-5 h-5 text-gold-600" />
              <span>Detailed Analysis</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {result.details.map((detail, index) => (
                <div key={index} className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-mystical-600 rounded-full mt-2 shrink-0" />
                  <Typography variant="p" locale={locale} className="text-sm">
                    {detail}
                  </Typography>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recommendations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Heart className="w-5 h-5 text-red-500" />
              <span>Recommendations</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {result.recommendations.map((recommendation, index) => (
                <div key={index} className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-gold-600 rounded-full mt-2 shrink-0" />
                  <Typography variant="p" locale={locale} className="text-sm">
                    {recommendation}
                  </Typography>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* AI Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="w-5 h-5 text-blue-600" />
            <span>AI-Generated Insights</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-muted/50 p-4 rounded-lg">
            <Typography variant="p" locale={locale} className="whitespace-pre-wrap">
              {result.aiInsights}
            </Typography>
          </div>
          
          <div className="mt-4 text-xs text-muted-foreground">
            Generated by {result.metadata.aiProvider} ({result.metadata.aiModel}) on{' '}
            {new Date(result.metadata.generatedAt).toLocaleDateString(locale)}
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Button
          onClick={handleShare}
          disabled={isSharing}
          className="flex items-center justify-center space-x-2"
        >
          <Share2 className="w-4 h-4" />
          <span>{isSharing ? 'Sharing...' : 'Share Results'}</span>
        </Button>

        <Button
          variant="outline"
          onClick={handleCopyLink}
          className="flex items-center justify-center space-x-2"
        >
          {copied ? (
            <>
              <Check className="w-4 h-4" />
              <span>Copied!</span>
            </>
          ) : (
            <>
              <Copy className="w-4 h-4" />
              <span>Copy Link</span>
            </>
          )}
        </Button>

        <Button
          variant="outline"
          onClick={handleDownloadPDF}
          className="flex items-center justify-center space-x-2"
        >
          <Download className="w-4 h-4" />
          <span>Download PDF</span>
        </Button>

        <Button
          variant="outline"
          onClick={onRetake}
          className="flex items-center justify-center space-x-2"
        >
          <RefreshCw className="w-4 h-4" />
          <span>Retake Test</span>
        </Button>
      </div>

      {/* Related Actions */}
      <Card>
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <Typography variant="h4" locale={locale}>
              What's Next?
            </Typography>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button
                onClick={onNewTest}
                variant="outline"
                className="flex items-center justify-center space-x-2"
              >
                <ExternalLink className="w-4 h-4" />
                <span>Try Another Test</span>
              </Button>
              
              <Button
                variant="outline"
                className="flex items-center justify-center space-x-2"
              >
                <Star className="w-4 h-4" />
                <span>Explore Related Content</span>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Disclaimer */}
      <Card className="bg-muted/30">
        <CardContent className="p-4">
          <Typography variant="small" locale={locale} className="text-center text-muted-foreground">
            This analysis is generated by AI and is for entertainment purposes only. 
            Results should not be considered as professional advice or absolute truth.
          </Typography>
        </CardContent>
      </Card>
    </div>
  );
}
