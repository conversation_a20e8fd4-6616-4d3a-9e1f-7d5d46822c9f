{"version": 3, "file": "subject-case.js", "sourceRoot": "", "sources": ["../src/subject-case.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,IAAI,UAAU,EAAE,MAAM,oBAAoB,CAAC;AACxD,OAAO,OAAO,MAAM,qBAAqB,CAAC;AAG1C;;;;;;;;;;;;;;GAcG;AACH,MAAM,qBAAqB,GAAG,yBAAyB,CAAC;AAExD,MAAM,OAAO,GAAG,CAAC,IAAa,EAAE,EAAE,CAAC,IAAI,KAAK,OAAO,CAAC;AAEpD,MAAM,CAAC,MAAM,WAAW,GAAgD,CACvE,MAAM,EACN,IAAI,GAAG,QAAQ,EACf,KAAK,GAAG,EAAE,EACT,EAAE;IACH,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;IAE3B,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC,EAAE,CAAC;QAC1E,OAAO,CAAC,IAAI,CAAC,CAAC;IACf,CAAC;IAED,MAAM,MAAM,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QACrE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC/B,OAAO;gBACN,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,KAAK;aACX,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACd,CAAC,CAAC,CAAC;IAEH,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACpC,MAAM,CAAC,GAAG,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QAC1C,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;IAEH,MAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAElD,OAAO;QACN,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;QAChC,OAAO,CAAC,CAAC,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,IAAI,EAAE,CAAC,CAAC;KACrE,CAAC;AACH,CAAC,CAAC"}