import { forwardRef, createContext, useContext } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, Card<PERSON>ooter, <PERSON>po<PERSON>, Button, Image } from '@/components/atoms';
import { cn } from '@/lib/utils';
import { Locale } from '@/types';

// 复合组件模式实现
interface DataCardContextValue {
  locale: Locale;
  variant: 'default' | 'blog' | 'product' | 'mystical';
}

const DataCardContext = createContext<DataCardContextValue>({
  locale: 'en',
  variant: 'default',
});

// Root component
interface DataCardRootProps {
  children: React.ReactNode;
  className?: string;
  locale?: Locale;
  variant?: 'default' | 'blog' | 'product' | 'mystical';
  onClick?: () => void;
}

const DataCardRoot = forwardRef<HTMLDivElement, DataCardRootProps>(
  ({ children, className, locale = 'en', variant = 'default', onClick, ...props }, ref) => {
    const variantStyles = {
      default: 'hover:shadow-md transition-shadow',
      blog: 'hover:shadow-mystical transition-all hover:scale-[1.02]',
      product: 'hover:shadow-lg transition-all hover:scale-105',
      mystical: 'bg-gradient-to-br from-mystical-50 to-gold-50 dark:from-mystical-900/20 dark:to-gold-900/20 border-mystical-300 hover:shadow-mystical-lg',
    };

    return (
      <DataCardContext.Provider value={{ locale, variant }}>
        <Card
          ref={ref}
          className={cn(
            'cursor-pointer',
            variantStyles[variant],
            className
          )}
          onClick={onClick}
          {...props}
        >
          {children}
        </Card>
      </DataCardContext.Provider>
    );
  }
);

// Header component
interface DataCardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

const DataCardHeader = forwardRef<HTMLDivElement, DataCardHeaderProps>(
  ({ children, className, ...props }, ref) => {
    return (
      <CardHeader ref={ref} className={cn('pb-3', className)} {...props}>
        {children}
      </CardHeader>
    );
  }
);

// Image component
interface DataCardImageProps {
  src: string;
  alt: string;
  aspectRatio?: 'square' | 'video' | '4/3' | '3/2' | '2/3';
  className?: string;
}

const DataCardImage = forwardRef<HTMLImageElement, DataCardImageProps>(
  ({ src, alt, aspectRatio = 'video', className, ...props }, ref) => {
    return (
      <div className="relative overflow-hidden rounded-t-lg">
        <Image
          ref={ref}
          src={src}
          alt={alt}
          aspectRatio={aspectRatio}
          className={cn('w-full', className)}
          {...props}
        />
      </div>
    );
  }
);

// Title component
interface DataCardTitleProps {
  children: React.ReactNode;
  className?: string;
  truncate?: boolean;
}

const DataCardTitle = forwardRef<HTMLHeadingElement, DataCardTitleProps>(
  ({ children, className, truncate = true, ...props }, ref) => {
    const { locale, variant } = useContext(DataCardContext);
    
    const variantStyles = {
      default: 'text-foreground',
      blog: 'text-mystical-900 dark:text-mystical-100',
      product: 'text-foreground',
      mystical: 'text-mystical-800 dark:text-mystical-200 font-mystical',
    };

    return (
      <Typography
        ref={ref as any}
        variant="h4"
        locale={locale}
        className={cn(
          variantStyles[variant],
          truncate && 'line-clamp-2',
          className
        )}
        {...props}
      >
        {children}
      </Typography>
    );
  }
);

// Description component
interface DataCardDescriptionProps {
  children: React.ReactNode;
  className?: string;
  lines?: number;
}

const DataCardDescription = forwardRef<HTMLParagraphElement, DataCardDescriptionProps>(
  ({ children, className, lines = 3, ...props }, ref) => {
    const { locale } = useContext(DataCardContext);

    return (
      <Typography
        ref={ref as any}
        variant="muted"
        locale={locale}
        className={cn(
          `line-clamp-${lines}`,
          className
        )}
        {...props}
      >
        {children}
      </Typography>
    );
  }
);

// Body component
interface DataCardBodyProps {
  children: React.ReactNode;
  className?: string;
}

const DataCardBody = forwardRef<HTMLDivElement, DataCardBodyProps>(
  ({ children, className, ...props }, ref) => {
    return (
      <CardContent ref={ref} className={cn('space-y-3', className)} {...props}>
        {children}
      </CardContent>
    );
  }
);

// Footer component
interface DataCardFooterProps {
  children: React.ReactNode;
  className?: string;
}

const DataCardFooter = forwardRef<HTMLDivElement, DataCardFooterProps>(
  ({ children, className, ...props }, ref) => {
    return (
      <CardFooter ref={ref} className={cn('pt-3', className)} {...props}>
        {children}
      </CardFooter>
    );
  }
);

// Actions component
interface DataCardActionsProps {
  children: React.ReactNode;
  className?: string;
  align?: 'left' | 'center' | 'right' | 'between';
}

const DataCardActions = forwardRef<HTMLDivElement, DataCardActionsProps>(
  ({ children, className, align = 'right', ...props }, ref) => {
    const alignStyles = {
      left: 'justify-start',
      center: 'justify-center',
      right: 'justify-end',
      between: 'justify-between',
    };

    return (
      <div
        ref={ref}
        className={cn(
          'flex items-center gap-2',
          alignStyles[align],
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

// Meta component for additional info
interface DataCardMetaProps {
  children: React.ReactNode;
  className?: string;
}

const DataCardMeta = forwardRef<HTMLDivElement, DataCardMetaProps>(
  ({ children, className, ...props }, ref) => {
    const { locale } = useContext(DataCardContext);

    return (
      <div
        ref={ref}
        className={cn('flex items-center gap-2 text-sm text-muted-foreground', className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);

// Export compound component
export const DataCard = {
  Root: DataCardRoot,
  Header: DataCardHeader,
  Image: DataCardImage,
  Title: DataCardTitle,
  Description: DataCardDescription,
  Body: DataCardBody,
  Footer: DataCardFooter,
  Actions: DataCardActions,
  Meta: DataCardMeta,
};

// Set display names
DataCardRoot.displayName = 'DataCard.Root';
DataCardHeader.displayName = 'DataCard.Header';
DataCardImage.displayName = 'DataCard.Image';
DataCardTitle.displayName = 'DataCard.Title';
DataCardDescription.displayName = 'DataCard.Description';
DataCardBody.displayName = 'DataCard.Body';
DataCardFooter.displayName = 'DataCard.Footer';
DataCardActions.displayName = 'DataCard.Actions';
DataCardMeta.displayName = 'DataCard.Meta';
