'use client';

import { ReactNode } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Locale } from '@/types';
import { getMultilingualLayoutClasses, getResponsiveFontSizeClasses } from '@/lib/multilingual-layout';
import { cn } from '@/lib/utils';

interface MobileCardProps {
  title?: string;
  children: ReactNode;
  className?: string;
  locale: Locale;
  variant?: 'default' | 'blog' | 'product' | 'test' | 'tarot';
  aspectRatio?: '16:9' | '4:3' | '1:1' | '2:3';
  image?: string;
  imageAlt?: string;
}

export function MobileCard({
  title,
  children,
  className,
  locale,
  variant = 'default',
  aspectRatio,
  image,
  imageAlt,
}: MobileCardProps) {
  const layoutClasses = getMultilingualLayoutClasses(locale);
  const titleFontClasses = getResponsiveFontSizeClasses(locale, 'lg');
  
  const variantStyles = {
    default: 'bg-white dark:bg-dark-800 border-mystical-200 dark:border-dark-700',
    blog: 'bg-white dark:bg-dark-800 border-mystical-200 dark:border-dark-700 hover:shadow-mystical transition-shadow',
    product: 'bg-white dark:bg-dark-800 border-mystical-200 dark:border-dark-700 hover:scale-105 transition-transform',
    test: 'bg-gradient-to-br from-mystical-50 to-gold-50 dark:from-mystical-900/20 dark:to-gold-900/20 border-mystical-300 dark:border-mystical-700',
    tarot: 'bg-gradient-to-br from-mystical-100 to-gold-100 dark:from-mystical-800 dark:to-gold-800 border-2 border-gold-400 dark:border-gold-600',
  };
  
  const aspectRatioClasses = {
    '16:9': 'aspect-video',
    '4:3': 'aspect-[4/3]',
    '1:1': 'aspect-square',
    '2:3': 'aspect-[2/3]',
  };

  return (
    <Card className={cn(
      'overflow-hidden',
      variantStyles[variant],
      layoutClasses,
      className
    )}>
      {/* Image section */}
      {image && aspectRatio && (
        <div className={cn(
          'relative overflow-hidden',
          aspectRatioClasses[aspectRatio]
        )}>
          <img
            src={image}
            alt={imageAlt || title || ''}
            className="absolute inset-0 w-full h-full object-cover"
            loading="lazy"
          />
          {variant === 'tarot' && (
            <div className="absolute inset-0 bg-gradient-to-t from-mystical-900/50 to-transparent" />
          )}
        </div>
      )}
      
      {/* Header section */}
      {title && (
        <CardHeader className="pb-3">
          <CardTitle className={cn(
            'line-clamp-2',
            titleFontClasses,
            variant === 'test' && 'text-center',
            variant === 'tarot' && 'text-mystical-800 dark:text-mystical-200'
          )}>
            {title}
          </CardTitle>
        </CardHeader>
      )}
      
      {/* Content section */}
      <CardContent className={cn(
        'pt-0',
        !title && 'pt-6'
      )}>
        {children}
      </CardContent>
    </Card>
  );
}

// 专门的移动端博客卡片
export function MobileBlogCard({
  title,
  excerpt,
  image,
  date,
  readTime,
  locale,
  href,
}: {
  title: string;
  excerpt: string;
  image: string;
  date: string;
  readTime: string;
  locale: Locale;
  href: string;
}) {
  const excerptFontClasses = getResponsiveFontSizeClasses(locale, 'sm');
  const metaFontClasses = getResponsiveFontSizeClasses(locale, 'xs');
  
  return (
    <MobileCard
      title={title}
      locale={locale}
      variant="blog"
      aspectRatio="16:9"
      image={image}
      imageAlt={title}
      className="cursor-pointer"
    >
      <div className="space-y-3">
        <p className={cn(
          'text-mystical-600 dark:text-mystical-400 line-clamp-3',
          excerptFontClasses
        )}>
          {excerpt}
        </p>
        
        <div className={cn(
          'flex items-center justify-between text-mystical-500 dark:text-mystical-500',
          metaFontClasses
        )}>
          <span>{date}</span>
          <span>{readTime}</span>
        </div>
      </div>
    </MobileCard>
  );
}

// 专门的移动端商品卡片
export function MobileProductCard({
  title,
  price,
  originalPrice,
  image,
  rating,
  locale,
  href,
}: {
  title: string;
  price: string;
  originalPrice?: string;
  image: string;
  rating: number;
  locale: Locale;
  href: string;
}) {
  const titleFontClasses = getResponsiveFontSizeClasses(locale, 'base');
  const priceFontClasses = getResponsiveFontSizeClasses(locale, 'lg');
  
  return (
    <MobileCard
      title={title}
      locale={locale}
      variant="product"
      aspectRatio="1:1"
      image={image}
      imageAlt={title}
      className="cursor-pointer"
    >
      <div className="space-y-3">
        <div className="flex items-center space-x-1">
          {[...Array(5)].map((_, i) => (
            <span
              key={i}
              className={cn(
                'text-sm',
                i < rating ? 'text-gold-400' : 'text-gray-300'
              )}
            >
              ★
            </span>
          ))}
          <span className="text-xs text-mystical-500 ml-2">
            ({rating})
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className={cn(
            'font-bold text-gold-600 dark:text-gold-400',
            priceFontClasses
          )}>
            {price}
          </span>
          {originalPrice && (
            <span className="text-sm text-mystical-500 line-through">
              {originalPrice}
            </span>
          )}
        </div>
      </div>
    </MobileCard>
  );
}
