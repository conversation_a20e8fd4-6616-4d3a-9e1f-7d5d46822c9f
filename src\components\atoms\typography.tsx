import { forwardRef } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { Locale } from '@/types';
import { getResponsiveFontSizeClasses, getLanguageFontFamily } from '@/lib/multilingual-layout';

const typographyVariants = cva(
  'text-foreground',
  {
    variants: {
      variant: {
        h1: 'scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl',
        h2: 'scroll-m-20 border-b pb-2 text-3xl font-semibold tracking-tight first:mt-0',
        h3: 'scroll-m-20 text-2xl font-semibold tracking-tight',
        h4: 'scroll-m-20 text-xl font-semibold tracking-tight',
        h5: 'scroll-m-20 text-lg font-semibold tracking-tight',
        h6: 'scroll-m-20 text-base font-semibold tracking-tight',
        p: 'leading-7 [&:not(:first-child)]:mt-6',
        blockquote: 'mt-6 border-l-2 pl-6 italic',
        code: 'relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold',
        lead: 'text-xl text-muted-foreground',
        large: 'text-lg font-semibold',
        small: 'text-sm font-medium leading-none',
        muted: 'text-sm text-muted-foreground',
        caption: 'text-xs text-muted-foreground',
        mystical: 'font-mystical text-mystical-800 dark:text-mystical-200',
      },
      align: {
        left: 'text-left',
        center: 'text-center',
        right: 'text-right',
        justify: 'text-justify',
      },
      weight: {
        light: 'font-light',
        normal: 'font-normal',
        medium: 'font-medium',
        semibold: 'font-semibold',
        bold: 'font-bold',
        extrabold: 'font-extrabold',
      },
    },
    defaultVariants: {
      variant: 'p',
      align: 'left',
      weight: 'normal',
    },
  }
);

export interface TypographyProps
  extends React.HTMLAttributes<HTMLElement>,
    VariantProps<typeof typographyVariants> {
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div' | 'blockquote' | 'code';
  locale?: Locale;
  truncate?: boolean;
  lineClamp?: number;
}

const Typography = forwardRef<HTMLElement, TypographyProps>(
  ({ 
    className, 
    variant, 
    align, 
    weight, 
    as, 
    locale = 'en', 
    truncate = false,
    lineClamp,
    children,
    ...props 
  }, ref) => {
    // 确定要使用的HTML元素
    const Component = as || (variant?.startsWith('h') ? variant : 'p') as keyof JSX.IntrinsicElements;
    
    // 获取多语言字体类名
    const fontFamily = getLanguageFontFamily(locale);
    
    // 构建类名
    const classes = cn(
      typographyVariants({ variant, align, weight }),
      fontFamily,
      truncate && 'truncate',
      lineClamp && `line-clamp-${lineClamp}`,
      className
    );

    return (
      <Component
        ref={ref as any}
        className={classes}
        dir={locale === 'ar' ? 'rtl' : 'ltr'}
        lang={locale}
        {...props}
      >
        {children}
      </Component>
    );
  }
);

Typography.displayName = 'Typography';

export { Typography, typographyVariants };
