import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// GET /api/tests/[slug] - 获取单个测试详情
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params;
    const { searchParams } = new URL(request.url);
    const locale = searchParams.get('locale') || 'en';

    const test = await prisma.mysticalTest.findUnique({
      where: { 
        slug,
        isActive: true,
      },
      include: {
        questions: {
          orderBy: { order: 'asc' },
        },
        _count: {
          select: {
            sessions: true,
          },
        },
      },
    });

    if (!test) {
      return NextResponse.json(
        { error: 'Test not found' },
        { status: 404 }
      );
    }

    // 增加浏览量
    await prisma.mysticalTest.update({
      where: { id: test.id },
      data: { views: { increment: 1 } },
    });

    // 格式化响应数据
    const formattedTest = {
      id: test.id,
      type: test.type,
      name: test.name,
      description: test.description,
      slug: test.slug,
      coverImage: test.coverImage,
      difficulty: test.difficulty,
      estimatedDuration: test.estimatedDuration,
      featured: test.featured,
      tags: test.tags,
      views: test.views + 1,
      completionCount: test._count.sessions,
      questions: test.questions.map(question => ({
        id: question.id,
        text: question.text,
        type: question.type,
        options: question.options,
        required: question.required,
        order: question.order,
      })),
      createdAt: test.createdAt,
      updatedAt: test.updatedAt,
    };

    return NextResponse.json(formattedTest);
  } catch (error) {
    console.error('Error fetching test:', error);
    return NextResponse.json(
      { error: 'Failed to fetch test' },
      { status: 500 }
    );
  }
}

// PUT /api/tests/[slug] - 更新测试（管理员功能）
export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params;
    const body = await request.json();

    // 验证请求数据
    const updateData = z.object({
      name: z.record(z.string()).optional(),
      description: z.record(z.string()).optional(),
      coverImage: z.string().optional(),
      difficulty: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED']).optional(),
      estimatedDuration: z.number().optional(),
      featured: z.boolean().optional(),
      tags: z.array(z.string()).optional(),
      isActive: z.boolean().optional(),
    }).parse(body);

    // 检查测试是否存在
    const existingTest = await prisma.mysticalTest.findUnique({
      where: { slug },
    });

    if (!existingTest) {
      return NextResponse.json(
        { error: 'Test not found' },
        { status: 404 }
      );
    }

    // 更新测试
    const updatedTest = await prisma.mysticalTest.update({
      where: { slug },
      data: updateData,
      include: {
        questions: {
          orderBy: { order: 'asc' },
        },
        _count: {
          select: {
            sessions: true,
          },
        },
      },
    });

    return NextResponse.json(updatedTest);
  } catch (error) {
    console.error('Error updating test:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update test' },
      { status: 500 }
    );
  }
}

// DELETE /api/tests/[slug] - 删除测试（软删除）
export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params;

    // 检查测试是否存在
    const existingTest = await prisma.mysticalTest.findUnique({
      where: { slug },
    });

    if (!existingTest) {
      return NextResponse.json(
        { error: 'Test not found' },
        { status: 404 }
      );
    }

    // 软删除：设置为不活跃
    await prisma.mysticalTest.update({
      where: { slug },
      data: { 
        isActive: false,
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({ message: 'Test deleted successfully' });
  } catch (error) {
    console.error('Error deleting test:', error);
    return NextResponse.json(
      { error: 'Failed to delete test' },
      { status: 500 }
    );
  }
}
