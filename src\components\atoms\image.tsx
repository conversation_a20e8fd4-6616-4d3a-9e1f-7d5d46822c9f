import { forwardRef, useState } from 'react';
import NextImage, { ImageProps as NextImageProps } from 'next/image';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { LoadingSpinner } from './loading-spinner';

const imageVariants = cva(
  'transition-opacity duration-300',
  {
    variants: {
      rounded: {
        none: 'rounded-none',
        sm: 'rounded-sm',
        md: 'rounded-md',
        lg: 'rounded-lg',
        xl: 'rounded-xl',
        '2xl': 'rounded-2xl',
        full: 'rounded-full',
      },
      objectFit: {
        contain: 'object-contain',
        cover: 'object-cover',
        fill: 'object-fill',
        none: 'object-none',
        'scale-down': 'object-scale-down',
      },
      aspectRatio: {
        square: 'aspect-square',
        video: 'aspect-video',
        '4/3': 'aspect-[4/3]',
        '3/2': 'aspect-[3/2]',
        '2/3': 'aspect-[2/3]',
        '16/9': 'aspect-video',
        '21/9': 'aspect-[21/9]',
      },
    },
    defaultVariants: {
      rounded: 'md',
      objectFit: 'cover',
    },
  }
);

export interface ImageProps
  extends Omit<NextImageProps, 'className'>,
    VariantProps<typeof imageVariants> {
  className?: string;
  containerClassName?: string;
  showLoading?: boolean;
  fallbackSrc?: string;
  onError?: () => void;
}

const Image = forwardRef<HTMLImageElement, ImageProps>(
  ({ 
    className,
    containerClassName,
    rounded,
    objectFit,
    aspectRatio,
    showLoading = true,
    fallbackSrc,
    onError,
    alt,
    ...props 
  }, ref) => {
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);
    const [currentSrc, setCurrentSrc] = useState(props.src);

    const handleLoad = () => {
      setIsLoading(false);
    };

    const handleError = () => {
      setIsLoading(false);
      setHasError(true);
      
      if (fallbackSrc && currentSrc !== fallbackSrc) {
        setCurrentSrc(fallbackSrc);
        setHasError(false);
        setIsLoading(true);
      } else {
        onError?.();
      }
    };

    return (
      <div className={cn(
        'relative overflow-hidden bg-muted',
        aspectRatio && imageVariants({ aspectRatio }),
        rounded && imageVariants({ rounded }),
        containerClassName
      )}>
        {/* Loading state */}
        {isLoading && showLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-muted">
            <LoadingSpinner size="sm" variant="muted" />
          </div>
        )}

        {/* Error state */}
        {hasError && !fallbackSrc && (
          <div className="absolute inset-0 flex items-center justify-center bg-muted text-muted-foreground">
            <div className="text-center">
              <div className="text-2xl mb-2">📷</div>
              <div className="text-sm">Image not found</div>
            </div>
          </div>
        )}

        {/* Image */}
        {!hasError && (
          <NextImage
            ref={ref}
            {...props}
            src={currentSrc}
            alt={alt}
            className={cn(
              imageVariants({ objectFit }),
              isLoading ? 'opacity-0' : 'opacity-100',
              className
            )}
            onLoad={handleLoad}
            onError={handleError}
          />
        )}
      </div>
    );
  }
);

Image.displayName = 'Image';

export { Image, imageVariants };
