'use client';

import { useState, useEffect, useCallback } from 'react';
import { LoadingSpinner } from '@/components/atoms';
import { ErrorBoundary } from '@/components/molecules';

// 渲染属性模式实现
export interface DataFetcherState<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  refetch: () => void;
}

export interface DataFetcherProps<T> {
  url: string;
  options?: RequestInit;
  children: (state: DataFetcherState<T>) => React.ReactNode;
  fallback?: React.ReactNode;
  errorFallback?: React.ReactNode;
  dependencies?: any[];
  enabled?: boolean;
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
}

export function DataFetcher<T = any>({
  url,
  options = {},
  children,
  fallback,
  errorFallback,
  dependencies = [],
  enabled = true,
  onSuccess,
  onError,
}: DataFetcherProps<T>) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async () => {
    if (!enabled) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      setData(result);
      onSuccess?.(result);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('An error occurred');
      setError(error);
      onError?.(error);
    } finally {
      setLoading(false);
    }
  }, [url, enabled, onSuccess, onError, ...dependencies]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const state: DataFetcherState<T> = {
    data,
    loading,
    error,
    refetch: fetchData,
  };

  // 如果有错误且提供了错误回退组件
  if (error && errorFallback) {
    return <>{errorFallback}</>;
  }

  // 如果正在加载且提供了回退组件
  if (loading && fallback) {
    return <>{fallback}</>;
  }

  return (
    <ErrorBoundary>
      {children(state)}
    </ErrorBoundary>
  );
}

// 预设的加载和错误组件
export const DefaultLoadingFallback = () => (
  <div className="flex items-center justify-center p-8">
    <LoadingSpinner size="lg" />
  </div>
);

export const DefaultErrorFallback = ({ error }: { error: Error }) => (
  <div className="flex items-center justify-center p-8 text-center">
    <div>
      <p className="text-red-600 dark:text-red-400 mb-2">
        Failed to load data
      </p>
      <p className="text-sm text-muted-foreground">
        {error.message}
      </p>
    </div>
  </div>
);

// 便捷的Hook版本
export function useDataFetcher<T = any>(
  url: string,
  options: Omit<DataFetcherProps<T>, 'children' | 'url'> = {}
): DataFetcherState<T> {
  const [state, setState] = useState<DataFetcherState<T>>({
    data: null,
    loading: false,
    error: null,
    refetch: () => {},
  });

  const fetchData = useCallback(async () => {
    if (!options.enabled) return;

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.options?.headers,
        },
        ...options.options,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      setState(prev => ({ ...prev, data: result, loading: false }));
      options.onSuccess?.(result);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('An error occurred');
      setState(prev => ({ ...prev, error, loading: false }));
      options.onError?.(error);
    }
  }, [url, options.enabled, options.onSuccess, options.onError, ...(options.dependencies || [])]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    ...state,
    refetch: fetchData,
  };
}
