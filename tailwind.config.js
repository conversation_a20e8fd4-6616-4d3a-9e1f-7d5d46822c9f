/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: '',
  theme: {
    container: {
      center: true,
      padding: {
        DEFAULT: '1rem',
        sm: '1.5rem',
        lg: '2rem',
        xl: '2rem',
        '2xl': '2rem',
      },
      screens: {
        sm: '640px',
        md: '768px',
        lg: '1024px',
        xl: '1280px',
        '2xl': '1440px',
      },
    },
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        // 主色调 - 神秘紫色系
        mystical: {
          50: '#faf7ff',   // 极浅紫，背景色
          100: '#f3ecff',  // 浅紫，卡片背景
          200: '#e9d8ff',  // 淡紫，悬停状态
          300: '#d8b9ff',  // 中浅紫，边框
          400: '#c084fc',  // 中紫，次要按钮
          500: '#a855f7',  // 标准紫，主按钮
          600: '#9333ea',  // 深紫，按钮悬停
          700: '#7c3aed',  // 更深紫，激活状态
          800: '#6b21a8',  // 很深紫，文字
          900: '#581c87',  // 最深紫，标题
        },
        // 辅助色 - 黄金色系
        gold: {
          50: '#fffbeb',   // 极浅金
          100: '#fef3c7',  // 浅金
          200: '#fde68a',  // 淡金
          300: '#fcd34d',  // 中浅金
          400: '#fbbf24',  // 中金
          500: '#f59e0b',  // 标准金，强调色
          600: '#d97706',  // 深金
          700: '#b45309',  // 更深金
          800: '#92400e',  // 很深金
          900: '#78350f',  // 最深金
        },
        // 深色系 - 神秘黑色系
        dark: {
          50: '#f8fafc',   // 极浅灰
          100: '#f1f5f9',  // 浅灰
          200: '#e2e8f0',  // 淡灰
          300: '#cbd5e1',  // 中浅灰
          400: '#94a3b8',  // 中灰
          500: '#64748b',  // 标准灰
          600: '#475569',  // 深灰
          700: '#334155',  // 更深灰
          800: '#1e293b',  // 很深灰，深色模式背景
          900: '#0f172a',  // 最深灰，深色模式主背景
        },
        // 星座色彩系统
        zodiac: {
          fire: '#ff6b6b',      // 火象星座 - 红色系
          earth: '#51cf66',     // 土象星座 - 绿色系
          air: '#74c0fc',       // 风象星座 - 蓝色系
          water: '#845ef7',     // 水象星座 - 紫色系
        },
        cosmic: {
          50: '#f0f4ff',
          100: '#e0e7ff',
          200: '#c7d2fe',
          300: '#a5b4fc',
          400: '#818cf8',
          500: '#6366f1',
          600: '#4f46e5',
          700: '#4338ca',
          800: '#3730a3',
          900: '#312e81',
          950: '#1e1b4b',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      fontFamily: {
        // 主要字体 - 现代无衬线
        sans: ['Inter', 'Noto Sans', 'system-ui', 'sans-serif'],
        // 标题字体 - 优雅衬线
        serif: ['Playfair Display', 'Noto Serif', 'Georgia', 'serif'],
        // 神秘字体 - 装饰性
        mystical: ['Cinzel', 'Trajan Pro', 'serif'],
        // 等宽字体 - 代码/数据
        mono: ['JetBrains Mono', 'Fira Code', 'Consolas', 'monospace'],

        // 多语言字体配置
        'zh': ['Noto Sans SC', 'PingFang SC', 'Microsoft YaHei', 'sans-serif'],
        'ja': ['Noto Sans JP', 'Hiragino Sans', 'Yu Gothic', 'sans-serif'],
        'ko': ['Noto Sans KR', 'Malgun Gothic', 'sans-serif'],
        'ar': ['Noto Sans Arabic', 'Tahoma', 'Arial', 'sans-serif'],
        'hi': ['Noto Sans Devanagari', 'Mangal', 'Arial Unicode MS', 'sans-serif'],
        'de': ['Noto Sans', 'Helvetica Neue', 'Arial', 'sans-serif'],
        'fr': ['Noto Sans', 'Helvetica Neue', 'Arial', 'sans-serif'],
        'it': ['Noto Sans', 'Helvetica Neue', 'Arial', 'sans-serif'],
        'ru': ['Noto Sans', 'Helvetica Neue', 'Arial', 'sans-serif'],
        'pt': ['Noto Sans', 'Helvetica Neue', 'Arial', 'sans-serif'],
        'es': ['Noto Sans', 'Helvetica Neue', 'Arial', 'sans-serif'],
      },
      boxShadow: {
        // 神秘学主题阴影
        'mystical': '0 10px 25px -3px rgba(168, 85, 247, 0.1), 0 4px 6px -2px rgba(168, 85, 247, 0.05)',
        'mystical-lg': '0 20px 40px -4px rgba(168, 85, 247, 0.15), 0 8px 16px -4px rgba(168, 85, 247, 0.1)',
        'gold': '0 10px 25px -3px rgba(245, 158, 11, 0.1), 0 4px 6px -2px rgba(245, 158, 11, 0.05)',
        'inner-mystical': 'inset 0 2px 4px 0 rgba(168, 85, 247, 0.1)',
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
        mysticalGlow: {
          '0%': {
            boxShadow: '0 0 20px rgba(168, 85, 247, 0.3)',
            transform: 'scale(1)'
          },
          '100%': {
            boxShadow: '0 0 40px rgba(168, 85, 247, 0.6)',
            transform: 'scale(1.02)'
          }
        },
        tarotFlip: {
          '0%': { transform: 'rotateY(0deg)' },
          '50%': { transform: 'rotateY(90deg)' },
          '100%': { transform: 'rotateY(0deg)' }
        },
        crystalShine: {
          '0%, 100%': { opacity: '0.8' },
          '50%': { opacity: '1', transform: 'scale(1.05)' }
        },
        starTwinkle: {
          '0%': { opacity: '0.5', transform: 'scale(0.8)' },
          '100%': { opacity: '1', transform: 'scale(1.2)' }
        },
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' }
        },
        slideUp: {
          '0%': { transform: 'translateY(100%)' },
          '100%': { transform: 'translateY(0)' }
        },
        scaleIn: {
          '0%': { transform: 'scale(0.9)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' }
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' }
        },
        'slide-in': {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
      },

      // 移动端触摸目标尺寸
      minHeight: {
        'touch': '44px',  // 最小触摸目标高度
        'touch-lg': '48px',  // 大触摸目标高度
      },
      minWidth: {
        'touch': '44px',  // 最小触摸目标宽度
        'touch-lg': '48px',  // 大触摸目标宽度
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        // 神秘学主题动画
        'mystical-glow': 'mysticalGlow 3s ease-in-out infinite alternate',
        'tarot-flip': 'tarotFlip 0.8s cubic-bezier(0.4, 0, 0.2, 1)',
        'crystal-shine': 'crystalShine 2s ease-in-out infinite',
        'star-twinkle': 'starTwinkle 1.5s ease-in-out infinite alternate',
        'fade-in': 'fadeIn 0.6s ease-out',
        'slide-up': 'slideUp 0.5s ease-out',
        'scale-in': 'scaleIn 0.3s ease-out',
        'float': 'float 3s ease-in-out infinite',
        'slide-in': 'slide-in 0.3s ease-out',
      },
      typography: {
        DEFAULT: {
          css: {
            maxWidth: 'none',
            color: 'hsl(var(--foreground))',
            h1: {
              color: 'hsl(var(--foreground))',
            },
            h2: {
              color: 'hsl(var(--foreground))',
            },
            h3: {
              color: 'hsl(var(--foreground))',
            },
            h4: {
              color: 'hsl(var(--foreground))',
            },
            strong: {
              color: 'hsl(var(--foreground))',
            },
            code: {
              color: 'hsl(var(--foreground))',
            },
            blockquote: {
              color: 'hsl(var(--muted-foreground))',
              borderLeftColor: 'hsl(var(--border))',
            },
          },
        },
      },
    },
  },
  plugins: [require('tailwindcss-animate'), require('@tailwindcss/typography')],
};
