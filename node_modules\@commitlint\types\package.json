{"name": "@commitlint/types", "type": "module", "version": "19.8.1", "description": "Shared types for commitlint packages", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib/"], "scripts": {"pkg": "pkg-check"}, "engines": {"node": ">=v18"}, "repository": {"type": "git", "url": "https://github.com/conventional-changelog/commitlint.git", "directory": "@commitlint/types"}, "bugs": {"url": "https://github.com/conventional-changelog/commitlint/issues"}, "homepage": "https://commitlint.js.org/", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"@types/conventional-commits-parser": "^5.0.0", "chalk": "^5.3.0"}, "devDependencies": {"@commitlint/utils": "^19.8.1"}, "gitHead": "3c302008cabeb0b08cd246b2417a51a9d745a918"}