import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

// UI状态接口
export interface UIState {
  // 主题设置
  theme: 'light' | 'dark' | 'system';
  
  // 布局状态
  sidebarOpen: boolean;
  mobileMenuOpen: boolean;
  
  // 模态框状态
  modal: {
    type: string;
    data?: any;
    isOpen: boolean;
  } | null;
  
  // 通知状态
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    title: string;
    message?: string;
    duration?: number;
  }>;
  
  // 加载状态
  loading: {
    global: boolean;
    [key: string]: boolean;
  };
  
  // 语言设置
  locale: string;
  
  // 动作
  toggleTheme: () => void;
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  toggleSidebar: () => void;
  setSidebarOpen: (open: boolean) => void;
  toggleMobileMenu: () => void;
  setMobileMenuOpen: (open: boolean) => void;
  openModal: (type: string, data?: any) => void;
  closeModal: () => void;
  addNotification: (notification: Omit<UIState['notifications'][0], 'id'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  setLoading: (key: string, loading: boolean) => void;
  setGlobalLoading: (loading: boolean) => void;
  setLocale: (locale: string) => void;
}

export const useUIStore = create<UIState>()(
  devtools(
    persist(
      (set, get) => ({
        // 初始状态
        theme: 'system',
        sidebarOpen: false,
        mobileMenuOpen: false,
        modal: null,
        notifications: [],
        loading: {
          global: false,
        },
        locale: 'en',

        // 动作实现
        toggleTheme: () => {
          const currentTheme = get().theme;
          const newTheme = currentTheme === 'light' ? 'dark' : 'light';
          set({ theme: newTheme });
        },

        setTheme: (theme) => {
          set({ theme });
        },

        toggleSidebar: () => {
          set((state) => ({ sidebarOpen: !state.sidebarOpen }));
        },

        setSidebarOpen: (open) => {
          set({ sidebarOpen: open });
        },

        toggleMobileMenu: () => {
          set((state) => ({ mobileMenuOpen: !state.mobileMenuOpen }));
        },

        setMobileMenuOpen: (open) => {
          set({ mobileMenuOpen: open });
        },

        openModal: (type, data) => {
          set({ modal: { type, data, isOpen: true } });
        },

        closeModal: () => {
          set({ modal: null });
        },

        addNotification: (notification) => {
          const id = Math.random().toString(36).substr(2, 9);
          set((state) => ({
            notifications: [...state.notifications, { ...notification, id }],
          }));

          // 自动移除通知
          if (notification.duration !== 0) {
            setTimeout(() => {
              get().removeNotification(id);
            }, notification.duration || 5000);
          }
        },

        removeNotification: (id) => {
          set((state) => ({
            notifications: state.notifications.filter((n) => n.id !== id),
          }));
        },

        clearNotifications: () => {
          set({ notifications: [] });
        },

        setLoading: (key, loading) => {
          set((state) => ({
            loading: {
              ...state.loading,
              [key]: loading,
            },
          }));
        },

        setGlobalLoading: (loading) => {
          set((state) => ({
            loading: {
              ...state.loading,
              global: loading,
            },
          }));
        },

        setLocale: (locale) => {
          set({ locale });
        },
      }),
      {
        name: 'ui-store',
        partialize: (state) => ({
          theme: state.theme,
          sidebarOpen: state.sidebarOpen,
          locale: state.locale,
        }),
      }
    ),
    {
      name: 'ui-store',
    }
  )
);

// 选择器函数
export const useTheme = () => useUIStore((state) => state.theme);
export const useSidebar = () => useUIStore((state) => ({
  isOpen: state.sidebarOpen,
  toggle: state.toggleSidebar,
  setOpen: state.setSidebarOpen,
}));
export const useMobileMenu = () => useUIStore((state) => ({
  isOpen: state.mobileMenuOpen,
  toggle: state.toggleMobileMenu,
  setOpen: state.setMobileMenuOpen,
}));
export const useModal = () => useUIStore((state) => ({
  modal: state.modal,
  open: state.openModal,
  close: state.closeModal,
}));
export const useNotifications = () => useUIStore((state) => ({
  notifications: state.notifications,
  add: state.addNotification,
  remove: state.removeNotification,
  clear: state.clearNotifications,
}));
export const useLoading = () => useUIStore((state) => ({
  loading: state.loading,
  setLoading: state.setLoading,
  setGlobalLoading: state.setGlobalLoading,
}));
export const useLocale = () => useUIStore((state) => ({
  locale: state.locale,
  setLocale: state.setLocale,
}));
