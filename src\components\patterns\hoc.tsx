import { ComponentType, forwardRef } from 'react';
import { useTranslations } from 'next-intl';
import { generateMetadata } from '@/components/seo/seo-head';
import { Locale } from '@/types';

// SEO增强HOC
export interface SEOConfig {
  title: string;
  description: string;
  keywords?: string[];
  canonical?: string;
  ogImage?: string;
  ogType?: 'website' | 'article' | 'product';
  noIndex?: boolean;
  noFollow?: boolean;
}

export interface WithSEOProps {
  seo?: Partial<SEOConfig>;
  locale?: Locale;
}

export function withSEO<P extends object>(
  Component: ComponentType<P>,
  defaultSEO: SEOConfig
) {
  const WrappedComponent = forwardRef<any, P & WithSEOProps>((props, ref) => {
    const { seo, locale = 'en', ...componentProps } = props;
    const seoConfig = { ...defaultSEO, ...seo };

    // 在客户端组件中，我们不能直接设置metadata
    // 这应该在页面级别处理
    return <Component ref={ref} {...(componentProps as P)} />;
  });

  WrappedComponent.displayName = `withSEO(${Component.displayName || Component.name})`;
  
  // 导出metadata生成函数供页面使用
  (WrappedComponent as any).generateMetadata = (props: WithSEOProps) => {
    const seoConfig = { ...defaultSEO, ...props.seo };
    return generateMetadata({
      ...seoConfig,
      locale: props.locale,
    });
  };

  return WrappedComponent;
}

// 多语言增强HOC
export interface WithI18nProps {
  locale?: Locale;
}

export function withI18n<P extends object>(
  Component: ComponentType<P>
) {
  const WrappedComponent = forwardRef<any, P & WithI18nProps>((props, ref) => {
    const t = useTranslations();
    const { locale = 'en', ...componentProps } = props;

    return (
      <Component
        ref={ref}
        {...(componentProps as P)}
        t={t}
        locale={locale}
      />
    );
  });

  WrappedComponent.displayName = `withI18n(${Component.displayName || Component.name})`;
  return WrappedComponent;
}

// 错误边界HOC
export interface WithErrorBoundaryProps {
  fallback?: React.ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

export function withErrorBoundary<P extends object>(
  Component: ComponentType<P>
) {
  const WrappedComponent = forwardRef<any, P & WithErrorBoundaryProps>((props, ref) => {
    const { fallback, onError, ...componentProps } = props;

    return (
      <ErrorBoundary fallback={fallback} onError={onError}>
        <Component ref={ref} {...(componentProps as P)} />
      </ErrorBoundary>
    );
  });

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
}

// 加载状态HOC
export interface WithLoadingProps {
  loading?: boolean;
  loadingFallback?: React.ReactNode;
}

export function withLoading<P extends object>(
  Component: ComponentType<P>
) {
  const WrappedComponent = forwardRef<any, P & WithLoadingProps>((props, ref) => {
    const { loading, loadingFallback, ...componentProps } = props;

    if (loading) {
      return loadingFallback || (
        <div className="flex items-center justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-mystical-600 border-t-transparent" />
        </div>
      );
    }

    return <Component ref={ref} {...(componentProps as P)} />;
  });

  WrappedComponent.displayName = `withLoading(${Component.displayName || Component.name})`;
  return WrappedComponent;
}

// 权限控制HOC
export interface WithAuthProps {
  requiredRole?: string;
  fallback?: React.ReactNode;
}

export function withAuth<P extends object>(
  Component: ComponentType<P>
) {
  const WrappedComponent = forwardRef<any, P & WithAuthProps>((props, ref) => {
    const { requiredRole, fallback, ...componentProps } = props;
    
    // 这里应该集成实际的认证逻辑
    // const { user, hasRole } = useAuth();
    
    // if (requiredRole && !hasRole(requiredRole)) {
    //   return fallback || <div>Access denied</div>;
    // }

    return <Component ref={ref} {...(componentProps as P)} />;
  });

  WrappedComponent.displayName = `withAuth(${Component.displayName || Component.name})`;
  return WrappedComponent;
}

// 组合多个HOC的工具函数
export function compose<P extends object>(...hocs: Array<(component: ComponentType<any>) => ComponentType<any>>) {
  return (Component: ComponentType<P>) => {
    return hocs.reduceRight((acc, hoc) => hoc(acc), Component);
  };
}

// 使用示例：
// const EnhancedComponent = compose(
//   withSEO(defaultSEO),
//   withI18n,
//   withErrorBoundary,
//   withLoading
// )(MyComponent);

import { ErrorBoundary } from '@/components/molecules';
