import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { TestType, TestDifficulty } from '@prisma/client';

// 查询参数验证模式
const querySchema = z.object({
  type: z.nativeEnum(TestType).optional(),
  difficulty: z.nativeEnum(TestDifficulty).optional(),
  featured: z.string().transform(Boolean).optional(),
  locale: z.string().default('en'),
  page: z.string().transform(Number).default('1'),
  limit: z.string().transform(Number).default('10'),
});

// GET /api/tests - 获取测试列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = querySchema.parse(Object.fromEntries(searchParams));

    const {
      type,
      difficulty,
      featured,
      locale,
      page,
      limit,
    } = query;

    // 构建查询条件
    const where: any = {
      isActive: true,
    };

    if (type) {
      where.type = type;
    }

    if (difficulty) {
      where.difficulty = difficulty;
    }

    if (featured !== undefined) {
      where.featured = featured;
    }

    // 计算分页
    const skip = (page - 1) * limit;

    // 查询测试
    const [tests, total] = await Promise.all([
      prisma.mysticalTest.findMany({
        where,
        skip,
        take: limit,
        orderBy: [
          { featured: 'desc' },
          { popularity: 'desc' },
          { createdAt: 'desc' },
        ],
        include: {
          questions: {
            select: {
              id: true,
              order: true,
            },
            orderBy: { order: 'asc' },
          },
          _count: {
            select: {
              sessions: true,
            },
          },
        },
      }),
      prisma.mysticalTest.count({ where }),
    ]);

    // 格式化响应数据
    const formattedTests = tests.map(test => ({
      id: test.id,
      type: test.type,
      name: test.name,
      description: test.description,
      slug: test.slug,
      coverImage: test.coverImage,
      difficulty: test.difficulty,
      estimatedDuration: test.estimatedDuration,
      questionCount: test.questions.length,
      completionCount: test._count.sessions,
      featured: test.featured,
      tags: test.tags,
      createdAt: test.createdAt,
    }));

    // 计算分页信息
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return NextResponse.json({
      tests: formattedTests,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      },
    });
  } catch (error) {
    console.error('Error fetching tests:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch tests' },
      { status: 500 }
    );
  }
}

// POST /api/tests - 创建新测试（管理员功能）
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 验证请求数据
    const testData = z.object({
      type: z.nativeEnum(TestType),
      name: z.record(z.string()),
      description: z.record(z.string()),
      slug: z.string(),
      coverImage: z.string().optional(),
      difficulty: z.nativeEnum(TestDifficulty),
      estimatedDuration: z.number(),
      featured: z.boolean().default(false),
      tags: z.array(z.string()).default([]),
      questions: z.array(z.object({
        text: z.record(z.string()),
        type: z.enum(['SINGLE_CHOICE', 'MULTIPLE_CHOICE', 'TEXT', 'SCALE', 'DATE']),
        options: z.record(z.array(z.string())).optional(),
        required: z.boolean().default(true),
        order: z.number(),
      })),
    }).parse(body);

    // 检查slug是否已存在
    const existingTest = await prisma.mysticalTest.findUnique({
      where: { slug: testData.slug },
    });

    if (existingTest) {
      return NextResponse.json(
        { error: 'Test with this slug already exists' },
        { status: 400 }
      );
    }

    // 创建测试
    const test = await prisma.mysticalTest.create({
      data: {
        type: testData.type,
        name: testData.name,
        description: testData.description,
        slug: testData.slug,
        coverImage: testData.coverImage,
        difficulty: testData.difficulty,
        estimatedDuration: testData.estimatedDuration,
        featured: testData.featured,
        tags: testData.tags,
        questions: {
          create: testData.questions.map(question => ({
            text: question.text,
            type: question.type,
            options: question.options,
            required: question.required,
            order: question.order,
          })),
        },
      },
      include: {
        questions: {
          orderBy: { order: 'asc' },
        },
      },
    });

    return NextResponse.json(test, { status: 201 });
  } catch (error) {
    console.error('Error creating test:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create test' },
      { status: 500 }
    );
  }
}
