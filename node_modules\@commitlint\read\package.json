{"name": "@commitlint/read", "type": "module", "version": "19.8.1", "description": "Read commit messages from a specified range or last edit", "main": "lib/read.js", "types": "lib/read.d.ts", "files": ["lib/"], "scripts": {"deps": "dep-check", "pkg": "pkg-check --skip-import"}, "engines": {"node": ">=v18"}, "repository": {"type": "git", "url": "https://github.com/conventional-changelog/commitlint.git", "directory": "@commitlint/read"}, "bugs": {"url": "https://github.com/conventional-changelog/commitlint/issues"}, "homepage": "https://commitlint.js.org/", "keywords": ["conventional-changelog", "commitlint", "library", "core"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@commitlint/test": "^19.8.1", "@commitlint/utils": "^19.8.1", "@types/git-raw-commits": "^2.0.3", "@types/minimist": "^1.2.4"}, "dependencies": {"@commitlint/top-level": "^19.8.1", "@commitlint/types": "^19.8.1", "git-raw-commits": "^4.0.0", "minimist": "^1.2.8", "tinyexec": "^1.0.0"}, "gitHead": "3c302008cabeb0b08cd246b2417a51a9d745a918"}