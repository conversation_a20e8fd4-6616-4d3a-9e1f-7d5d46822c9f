'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button, Input, Textarea, Switch } from '@/components/atoms';
import { FormField } from '@/components/molecules';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/atoms';
import { Progress } from '@/components/atoms';
import { Badge } from '@/components/atoms';
import { Upload, FileText, CheckCircle, XCircle, Clock, AlertCircle } from 'lucide-react';
import { ImportStatus, ImportItemStatus } from '@prisma/client';

interface ImportFile {
  file: File;
  content?: string;
  status: 'pending' | 'processing' | 'success' | 'error';
  error?: string;
  preview?: {
    title: string;
    excerpt: string;
    wordCount: number;
  };
}

interface ImportConfig {
  name: string;
  description: string;
  autoPublish: boolean;
  defaultCategory: string;
  defaultLocale: string;
  overwriteExisting: boolean;
  generateSEO: boolean;
  optimizeImages: boolean;
}

interface BulkImportProps {
  categories: Array<{ id: string; name: Record<string, string> }>;
  onImportComplete: (taskId: string) => void;
}

export function BulkImport({ categories, onImportComplete }: BulkImportProps) {
  const [files, setFiles] = useState<ImportFile[]>([]);
  const [config, setConfig] = useState<ImportConfig>({
    name: '',
    description: '',
    autoPublish: false,
    defaultCategory: '',
    defaultLocale: 'en',
    overwriteExisting: false,
    generateSEO: true,
    optimizeImages: true,
  });
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState<'upload' | 'configure' | 'preview' | 'import'>('upload');

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const newFiles: ImportFile[] = [];

    for (const file of acceptedFiles) {
      if (file.type === 'text/markdown' || file.name.endsWith('.md') || file.name.endsWith('.mdx')) {
        try {
          const content = await file.text();
          const preview = generatePreview(content);
          
          newFiles.push({
            file,
            content,
            status: 'pending',
            preview,
          });
        } catch (error) {
          newFiles.push({
            file,
            status: 'error',
            error: 'Failed to read file content',
          });
        }
      } else {
        newFiles.push({
          file,
          status: 'error',
          error: 'Unsupported file type. Only Markdown files are supported.',
        });
      }
    }

    setFiles(prev => [...prev, ...newFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/markdown': ['.md', '.mdx'],
      'text/plain': ['.txt'],
    },
    multiple: true,
  });

  const generatePreview = (content: string) => {
    // 简单的Markdown解析预览
    const lines = content.split('\n');
    const title = lines.find(line => line.startsWith('# '))?.replace('# ', '') || 'Untitled';
    const excerpt = lines.slice(0, 5).join(' ').substring(0, 150) + '...';
    const wordCount = content.split(/\s+/).length;

    return { title, excerpt, wordCount };
  };

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleImport = async () => {
    if (!config.name || !config.defaultCategory || files.length === 0) {
      return;
    }

    setIsImporting(true);
    setCurrentStep('import');
    setImportProgress(0);

    try {
      const importData = {
        files: files.map(f => ({
          name: f.file.name,
          content: f.content || '',
          size: f.file.size,
        })),
        config,
      };

      const response = await fetch('/api/blog/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(importData),
      });

      if (!response.ok) {
        throw new Error('Import failed');
      }

      const result = await response.json();
      
      // 轮询导入状态
      await pollImportStatus(result.taskId);
      
      onImportComplete(result.taskId);
    } catch (error) {
      console.error('Import error:', error);
    } finally {
      setIsImporting(false);
    }
  };

  const pollImportStatus = async (taskId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/api/blog/import/${taskId}`);
        const task = await response.json();

        if (task.status === ImportStatus.COMPLETED || task.status === ImportStatus.FAILED) {
          clearInterval(pollInterval);
          setImportProgress(100);
        } else if (task.status === ImportStatus.PROCESSING) {
          const progress = (task.processedFiles / task.totalFiles) * 100;
          setImportProgress(progress);
        }
      } catch (error) {
        console.error('Error polling import status:', error);
        clearInterval(pollInterval);
      }
    }, 1000);
  };

  const getStatusIcon = (status: ImportFile['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'processing':
        return <Clock className="w-4 h-4 text-blue-600 animate-spin" />;
      default:
        return <FileText className="w-4 h-4 text-gray-600" />;
    }
  };

  const validFiles = files.filter(f => f.status !== 'error');
  const errorFiles = files.filter(f => f.status === 'error');

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-2">Bulk Import Articles</h1>
        <p className="text-muted-foreground">
          Import multiple Markdown files to create articles in bulk
        </p>
      </div>

      {/* Step Indicator */}
      <div className="flex items-center justify-center space-x-4 mb-8">
        {['upload', 'configure', 'preview', 'import'].map((step, index) => (
          <div
            key={step}
            className={`flex items-center ${
              currentStep === step ? 'text-mystical-600' : 'text-gray-400'
            }`}
          >
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep === step
                  ? 'bg-mystical-600 text-white'
                  : 'bg-gray-200 text-gray-600'
              }`}
            >
              {index + 1}
            </div>
            <span className="ml-2 capitalize">{step}</span>
            {index < 3 && <div className="w-8 h-px bg-gray-300 ml-4" />}
          </div>
        ))}
      </div>

      {currentStep === 'upload' && (
        <Card>
          <CardHeader>
            <CardTitle>Upload Files</CardTitle>
          </CardHeader>
          <CardContent>
            {/* File Drop Zone */}
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                isDragActive
                  ? 'border-mystical-500 bg-mystical-50'
                  : 'border-gray-300 hover:border-mystical-400'
              }`}
            >
              <input {...getInputProps()} />
              <Upload className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <p className="text-lg font-medium mb-2">
                {isDragActive ? 'Drop files here' : 'Drag & drop files here'}
              </p>
              <p className="text-muted-foreground mb-4">
                or click to select files
              </p>
              <p className="text-sm text-muted-foreground">
                Supports: .md, .mdx files
              </p>
            </div>

            {/* File List */}
            {files.length > 0 && (
              <div className="mt-6 space-y-2">
                <h3 className="font-medium">Uploaded Files ({files.length})</h3>
                <div className="max-h-60 overflow-y-auto space-y-2">
                  {files.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(file.status)}
                        <div>
                          <p className="font-medium">{file.file.name}</p>
                          {file.preview && (
                            <p className="text-sm text-muted-foreground">
                              {file.preview.title} • {file.preview.wordCount} words
                            </p>
                          )}
                          {file.error && (
                            <p className="text-sm text-red-600">{file.error}</p>
                          )}
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(index)}
                      >
                        Remove
                      </Button>
                    </div>
                  ))}
                </div>

                {validFiles.length > 0 && (
                  <div className="flex justify-end mt-4">
                    <Button onClick={() => setCurrentStep('configure')}>
                      Configure Import ({validFiles.length} files)
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {currentStep === 'configure' && (
        <Card>
          <CardHeader>
            <CardTitle>Import Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              label="Import Name"
              required
            >
              <Input
                value={config.name}
                onChange={(e) => setConfig(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter import task name"
              />
            </FormField>

            <FormField
              label="Description"
            >
              <Textarea
                value={config.description}
                onChange={(e) => setConfig(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe this import task"
                rows={3}
              />
            </FormField>

            <FormField
              label="Default Category"
              required
            >
              <select
                value={config.defaultCategory}
                onChange={(e) => setConfig(prev => ({ ...prev, defaultCategory: e.target.value }))}
                className="w-full p-2 border rounded-md"
              >
                <option value="">Select category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name[config.defaultLocale] || Object.values(category.name)[0]}
                  </option>
                ))}
              </select>
            </FormField>

            <div className="grid grid-cols-2 gap-4">
              <FormField label="Auto Publish">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={config.autoPublish}
                    onCheckedChange={(checked) => setConfig(prev => ({ ...prev, autoPublish: checked }))}
                  />
                  <span className="text-sm">Publish articles immediately</span>
                </div>
              </FormField>

              <FormField label="Overwrite Existing">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={config.overwriteExisting}
                    onCheckedChange={(checked) => setConfig(prev => ({ ...prev, overwriteExisting: checked }))}
                  />
                  <span className="text-sm">Overwrite existing articles</span>
                </div>
              </FormField>

              <FormField label="Generate SEO">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={config.generateSEO}
                    onCheckedChange={(checked) => setConfig(prev => ({ ...prev, generateSEO: checked }))}
                  />
                  <span className="text-sm">Auto-generate SEO metadata</span>
                </div>
              </FormField>

              <FormField label="Optimize Images">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={config.optimizeImages}
                    onCheckedChange={(checked) => setConfig(prev => ({ ...prev, optimizeImages: checked }))}
                  />
                  <span className="text-sm">Optimize and compress images</span>
                </div>
              </FormField>
            </div>

            <div className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => setCurrentStep('upload')}
              >
                Back
              </Button>
              <Button
                onClick={() => setCurrentStep('preview')}
                disabled={!config.name || !config.defaultCategory}
              >
                Preview Import
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {currentStep === 'preview' && (
        <Card>
          <CardHeader>
            <CardTitle>Import Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="font-medium mb-2">Import Summary</h3>
                <ul className="text-sm space-y-1">
                  <li>• {validFiles.length} files will be imported</li>
                  <li>• Articles will be {config.autoPublish ? 'published' : 'saved as drafts'}</li>
                  <li>• Default category: {categories.find(c => c.id === config.defaultCategory)?.name[config.defaultLocale]}</li>
                  <li>• SEO generation: {config.generateSEO ? 'Enabled' : 'Disabled'}</li>
                </ul>
              </div>

              {errorFiles.length > 0 && (
                <div className="bg-red-50 p-4 rounded-lg">
                  <h3 className="font-medium mb-2 text-red-800">
                    <AlertCircle className="w-4 h-4 inline mr-1" />
                    Files with Errors ({errorFiles.length})
                  </h3>
                  <ul className="text-sm space-y-1 text-red-700">
                    {errorFiles.map((file, index) => (
                      <li key={index}>• {file.file.name}: {file.error}</li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={() => setCurrentStep('configure')}
                >
                  Back
                </Button>
                <Button
                  onClick={handleImport}
                  disabled={validFiles.length === 0}
                >
                  Start Import ({validFiles.length} files)
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {currentStep === 'import' && (
        <Card>
          <CardHeader>
            <CardTitle>Importing Articles</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-2xl font-bold mb-2">{Math.round(importProgress)}%</div>
                <Progress value={importProgress} className="w-full" />
                <p className="text-muted-foreground mt-2">
                  {isImporting ? 'Processing files...' : 'Import completed!'}
                </p>
              </div>

              {!isImporting && (
                <div className="text-center">
                  <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
                  <p className="text-lg font-medium">Import Completed Successfully!</p>
                  <p className="text-muted-foreground">
                    All files have been processed and articles created.
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
