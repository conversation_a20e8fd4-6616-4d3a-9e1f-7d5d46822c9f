import { forwardRef } from 'react';
import { Input, Typography } from '@/components/atoms';
import { cn } from '@/lib/utils';
import { Locale } from '@/types';

export interface FormFieldProps {
  label?: string;
  description?: string;
  error?: string;
  required?: boolean;
  className?: string;
  locale?: Locale;
  children?: React.ReactNode;
  // Input props
  type?: string;
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  name?: string;
  id?: string;
}

const FormField = forwardRef<HTMLInputElement, FormFieldProps>(
  ({
    label,
    description,
    error,
    required = false,
    className,
    locale = 'en',
    children,
    type = 'text',
    placeholder,
    value,
    onChange,
    disabled = false,
    name,
    id,
    ...props
  }, ref) => {
    const fieldId = id || name || `field-${Math.random().toString(36).substr(2, 9)}`;

    return (
      <div className={cn('space-y-2', className)}>
        {/* Label */}
        {label && (
          <label
            htmlFor={fieldId}
            className="block"
          >
            <Typography
              variant="small"
              weight="medium"
              locale={locale}
              className="text-foreground"
            >
              {label}
              {required && (
                <span className="text-red-500 ml-1" aria-label="required">
                  *
                </span>
              )}
            </Typography>
          </label>
        )}

        {/* Description */}
        {description && (
          <Typography
            variant="muted"
            locale={locale}
            className="text-muted-foreground"
          >
            {description}
          </Typography>
        )}

        {/* Input or custom children */}
        <div className="relative">
          {children || (
            <Input
              ref={ref}
              id={fieldId}
              name={name}
              type={type}
              placeholder={placeholder}
              value={value}
              onChange={onChange}
              disabled={disabled}
              required={required}
              aria-invalid={error ? 'true' : 'false'}
              aria-describedby={
                error ? `${fieldId}-error` : 
                description ? `${fieldId}-description` : 
                undefined
              }
              className={cn(
                error && 'border-red-500 focus:border-red-500 focus:ring-red-500'
              )}
              {...props}
            />
          )}
        </div>

        {/* Error message */}
        {error && (
          <Typography
            variant="small"
            locale={locale}
            className="text-red-600 dark:text-red-400"
            id={`${fieldId}-error`}
            role="alert"
          >
            {error}
          </Typography>
        )}
      </div>
    );
  }
);

FormField.displayName = 'FormField';

export { FormField };
