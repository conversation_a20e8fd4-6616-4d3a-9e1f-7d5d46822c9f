{"name": "is-text-path", "version": "2.0.0", "description": "Check if a file path is a text file", "license": "MIT", "repository": "sindresorhus/is-text-path", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["text", "extensions", "extension", "file", "path", "check", "detect", "is"], "dependencies": {"text-extensions": "^2.0.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}