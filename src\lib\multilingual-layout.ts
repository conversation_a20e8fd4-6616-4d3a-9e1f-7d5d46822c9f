import { Locale } from '@/types';

// 多语言布局适配工具

// 文字方向配置
export const TEXT_DIRECTION_CONFIG = {
  ltr: ['en', 'zh', 'es', 'pt', 'hi', 'ja', 'de', 'fr', 'it', 'ru', 'ko'] as Locale[],
  rtl: ['ar'] as Locale[],
} as const;

// 语言特定的文字长度扩展比例
export const TEXT_EXPANSION_RATIOS: Record<Locale, number> = {
  en: 1.0,    // 基准
  zh: 0.8,    // 中文通常比英语短20%
  es: 1.25,   // 西班牙语比英语长25%
  pt: 1.2,    // 葡萄牙语比英语长20%
  hi: 1.4,    // 印地语比英语长40%
  ja: 1.1,    // 日语比英语长10%
  de: 1.3,    // 德语通常比英语长30%
  fr: 1.2,    // 法语比英语长20%
  it: 1.15,   // 意大利语比英语长15%
  ru: 1.15,   // 俄语比英语长15%
  ko: 1.1,    // 韩语比英语长10%
  ar: 1.2,    // 阿拉伯语比英语长20%
};

// 字体大小调整配置
export const FONT_SIZE_ADJUSTMENTS: Record<Locale, number> = {
  en: 1.0,    // 基准
  zh: 1.1,    // 中文字体稍大
  es: 1.0,    // 西班牙语标准
  pt: 1.0,    // 葡萄牙语标准
  hi: 1.1,    // 印地语字体稍大
  ja: 1.1,    // 日语字体稍大
  de: 1.0,    // 德语标准
  fr: 1.0,    // 法语标准
  it: 1.0,    // 意大利语标准
  ru: 1.0,    // 俄语标准
  ko: 1.1,    // 韩语字体稍大
  ar: 1.05,   // 阿拉伯语字体稍大
};

// 移动端字体大小额外调整
export const MOBILE_FONT_ADJUSTMENTS: Record<Locale, number> = {
  en: 1.1,    // 移动端所有语言字体放大10%
  zh: 1.2,    // 中文移动端字体放大20%
  es: 1.1,    // 西班牙语移动端字体放大10%
  pt: 1.1,    // 葡萄牙语移动端字体放大10%
  hi: 1.15,   // 印地语移动端字体放大15%
  ja: 1.15,   // 日语移动端字体放大15%
  de: 1.1,    // 德语移动端字体放大10%
  fr: 1.1,    // 法语移动端字体放大10%
  it: 1.1,    // 意大利语移动端字体放大10%
  ru: 1.1,    // 俄语移动端字体放大10%
  ko: 1.15,   // 韩语移动端字体放大15%
  ar: 1.15,   // 阿拉伯语移动端字体放大15%
};

// 获取文字方向
export function getTextDirection(locale: Locale): 'ltr' | 'rtl' {
  return TEXT_DIRECTION_CONFIG.rtl.includes(locale) ? 'rtl' : 'ltr';
}

// 获取语言特定的字体族
export function getLanguageFontFamily(locale: Locale): string {
  const fontFamilies: Record<Locale, string> = {
    en: 'font-sans',
    zh: 'font-zh',
    es: 'font-es',
    pt: 'font-pt',
    hi: 'font-hi',
    ja: 'font-ja',
    de: 'font-de',
    fr: 'font-fr',
    it: 'font-it',
    ru: 'font-ru',
    ko: 'font-ko',
    ar: 'font-ar',
  };
  
  return fontFamilies[locale] || 'font-sans';
}

// 获取布局方向类名
export function getLayoutDirectionClasses(locale: Locale): string {
  const direction = getTextDirection(locale);
  const baseClasses = direction === 'rtl' ? 'rtl' : 'ltr';
  
  return `${baseClasses} ${direction === 'rtl' ? 'text-right' : 'text-left'}`;
}

// 获取响应式字体大小类名
export function getResponsiveFontSizeClasses(
  locale: Locale,
  baseSize: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl'
): string {
  const adjustment = FONT_SIZE_ADJUSTMENTS[locale];
  const mobileAdjustment = MOBILE_FONT_ADJUSTMENTS[locale];
  
  // 根据调整比例选择合适的字体大小
  const sizeMap = {
    xs: adjustment >= 1.1 ? 'text-sm' : 'text-xs',
    sm: adjustment >= 1.1 ? 'text-base' : 'text-sm',
    base: adjustment >= 1.1 ? 'text-lg' : 'text-base',
    lg: adjustment >= 1.1 ? 'text-xl' : 'text-lg',
    xl: adjustment >= 1.1 ? 'text-2xl' : 'text-xl',
    '2xl': adjustment >= 1.1 ? 'text-3xl' : 'text-2xl',
    '3xl': adjustment >= 1.1 ? 'text-4xl' : 'text-3xl',
    '4xl': adjustment >= 1.1 ? 'text-5xl' : 'text-4xl',
  };
  
  const mobileSizeMap = {
    xs: mobileAdjustment >= 1.15 ? 'sm:text-base' : 'sm:text-sm',
    sm: mobileAdjustment >= 1.15 ? 'sm:text-lg' : 'sm:text-base',
    base: mobileAdjustment >= 1.15 ? 'sm:text-xl' : 'sm:text-lg',
    lg: mobileAdjustment >= 1.15 ? 'sm:text-2xl' : 'sm:text-xl',
    xl: mobileAdjustment >= 1.15 ? 'sm:text-3xl' : 'sm:text-2xl',
    '2xl': mobileAdjustment >= 1.15 ? 'sm:text-4xl' : 'sm:text-3xl',
    '3xl': mobileAdjustment >= 1.15 ? 'sm:text-5xl' : 'sm:text-4xl',
    '4xl': mobileAdjustment >= 1.15 ? 'sm:text-6xl' : 'sm:text-5xl',
  };
  
  return `${sizeMap[baseSize]} ${mobileSizeMap[baseSize]}`;
}

// 获取文化敏感的颜色配置
export function getCulturalColors(locale: Locale) {
  const culturalColors: Record<Locale, { lucky: string; unlucky: string; primary: string }> = {
    en: { lucky: 'text-green-600', unlucky: 'text-red-600', primary: 'text-mystical-600' },
    zh: { lucky: 'text-red-600', unlucky: 'text-gray-400', primary: 'text-red-700' },
    es: { lucky: 'text-green-600', unlucky: 'text-red-600', primary: 'text-mystical-600' },
    pt: { lucky: 'text-green-600', unlucky: 'text-red-600', primary: 'text-mystical-600' },
    hi: { lucky: 'text-orange-600', unlucky: 'text-gray-800', primary: 'text-orange-700' },
    ja: { lucky: 'text-red-600', unlucky: 'text-gray-800', primary: 'text-red-700' },
    de: { lucky: 'text-green-600', unlucky: 'text-red-600', primary: 'text-mystical-600' },
    fr: { lucky: 'text-green-600', unlucky: 'text-red-600', primary: 'text-mystical-600' },
    it: { lucky: 'text-green-600', unlucky: 'text-red-600', primary: 'text-mystical-600' },
    ru: { lucky: 'text-green-600', unlucky: 'text-red-600', primary: 'text-mystical-600' },
    ko: { lucky: 'text-red-600', unlucky: 'text-gray-800', primary: 'text-red-700' },
    ar: { lucky: 'text-green-600', unlucky: 'text-yellow-600', primary: 'text-green-700' },
  };
  
  return culturalColors[locale] || culturalColors.en;
}

// 获取布局间距调整
export function getLayoutSpacing(locale: Locale): string {
  const expansionRatio = TEXT_EXPANSION_RATIOS[locale];
  
  if (expansionRatio > 1.2) {
    return 'space-y-6 md:space-y-8'; // 更大间距用于长文本语言
  } else if (expansionRatio < 0.9) {
    return 'space-y-3 md:space-y-4'; // 更小间距用于紧凑语言
  }
  
  return 'space-y-4 md:space-y-6'; // 标准间距
}

// 获取完整的多语言布局类名
export function getMultilingualLayoutClasses(locale: Locale): string {
  const direction = getLayoutDirectionClasses(locale);
  const fontFamily = getLanguageFontFamily(locale);
  const spacing = getLayoutSpacing(locale);
  
  return `${direction} ${fontFamily} ${spacing}`;
}

// 导出便捷函数
export function applyMultilingualStyles(locale: Locale, element: HTMLElement) {
  const classes = getMultilingualLayoutClasses(locale);
  element.className = `${element.className} ${classes}`;
  
  // 设置文字方向属性
  element.dir = getTextDirection(locale);
  
  // 设置语言属性
  element.lang = locale;
}
