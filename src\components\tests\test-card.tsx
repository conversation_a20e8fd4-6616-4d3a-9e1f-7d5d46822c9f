'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardHeader } from '@/components/atoms';
import { Badge } from '@/components/atoms';
import { Button } from '@/components/atoms';
import { Typography } from '@/components/atoms';
import { Image } from '@/components/atoms';
import { Progress } from '@/components/atoms';
import { 
  Clock, 
  Users, 
  Star, 
  Play, 
  Eye,
  TrendingUp,
  Award
} from 'lucide-react';
import { TestType, TestDifficulty } from '@prisma/client';
import { Locale } from '@/types';
import { getMultilingualLayoutClasses } from '@/lib/multilingual-layout';

interface TestCardProps {
  test: {
    id: string;
    type: TestType;
    name: Record<string, string>;
    description: Record<string, string>;
    slug: string;
    coverImage?: string;
    difficulty: TestDifficulty;
    estimatedDuration: number;
    questionCount: number;
    completionCount: number;
    featured: boolean;
    tags: string[];
    views?: number;
  };
  locale: Locale;
  variant?: 'default' | 'featured' | 'compact';
  showStats?: boolean;
  onStart?: (testId: string) => void;
}

export function TestCard({
  test,
  locale,
  variant = 'default',
  showStats = true,
  onStart,
}: TestCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const layoutClasses = getMultilingualLayoutClasses(locale);

  const getDifficultyColor = (difficulty: TestDifficulty) => {
    switch (difficulty) {
      case TestDifficulty.BEGINNER:
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case TestDifficulty.INTERMEDIATE:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case TestDifficulty.ADVANCED:
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getTypeIcon = (type: TestType) => {
    switch (type) {
      case TestType.TAROT:
        return '🔮';
      case TestType.ASTROLOGY:
        return '⭐';
      case TestType.NUMEROLOGY:
        return '🔢';
      case TestType.PERSONALITY:
        return '🧠';
      case TestType.COMPATIBILITY:
        return '💕';
      default:
        return '✨';
    }
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}min`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}min` : `${hours}h`;
  };

  const formatCount = (count: number) => {
    if (count < 1000) return count.toString();
    if (count < 1000000) return `${(count / 1000).toFixed(1)}K`;
    return `${(count / 1000000).toFixed(1)}M`;
  };

  const handleStartTest = () => {
    if (onStart) {
      onStart(test.id);
    }
  };

  if (variant === 'compact') {
    return (
      <Card 
        className={`
          cursor-pointer transition-all duration-200 hover:shadow-mystical
          ${test.featured ? 'ring-2 ring-gold-400' : ''}
          ${layoutClasses}
        `}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <div className="text-2xl">{getTypeIcon(test.type)}</div>
            <div className="flex-1 min-w-0">
              <Typography
                variant="h6"
                locale={locale}
                className="truncate"
              >
                {test.name[locale] || test.name.en}
              </Typography>
              <div className="flex items-center space-x-2 mt-1">
                <Badge className={getDifficultyColor(test.difficulty)}>
                  {test.difficulty}
                </Badge>
                <span className="text-sm text-muted-foreground flex items-center">
                  <Clock className="w-3 h-3 mr-1" />
                  {formatDuration(test.estimatedDuration)}
                </span>
              </div>
            </div>
            <Button
              size="sm"
              onClick={handleStartTest}
              className="shrink-0"
            >
              <Play className="w-4 h-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card 
      className={`
        group cursor-pointer transition-all duration-300 hover:shadow-mystical-lg
        ${test.featured ? 'ring-2 ring-gold-400 bg-gradient-to-br from-mystical-50 to-gold-50 dark:from-mystical-900/20 dark:to-gold-900/20' : ''}
        ${isHovered ? 'scale-[1.02]' : ''}
        ${layoutClasses}
      `}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Featured Badge */}
      {test.featured && (
        <div className="absolute top-3 right-3 z-10">
          <Badge className="bg-gold-500 text-white">
            <Star className="w-3 h-3 mr-1" />
            Featured
          </Badge>
        </div>
      )}

      {/* Cover Image */}
      {test.coverImage && (
        <div className="relative overflow-hidden rounded-t-lg">
          <Image
            src={test.coverImage}
            alt={test.name[locale] || test.name.en}
            aspectRatio="16:9"
            className="transition-transform duration-300 group-hover:scale-105"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
          
          {/* Type Icon Overlay */}
          <div className="absolute top-3 left-3">
            <div className="w-10 h-10 bg-white/90 dark:bg-black/90 rounded-full flex items-center justify-center text-xl">
              {getTypeIcon(test.type)}
            </div>
          </div>
        </div>
      )}

      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <Typography
              variant="h4"
              locale={locale}
              className="line-clamp-2 group-hover:text-mystical-600 transition-colors"
            >
              {test.name[locale] || test.name.en}
            </Typography>
            
            <Typography
              variant="muted"
              locale={locale}
              className="line-clamp-2 mt-2"
            >
              {test.description[locale] || test.description.en}
            </Typography>
          </div>
        </div>

        {/* Tags */}
        {test.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-3">
            {test.tags.slice(0, 3).map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {test.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{test.tags.length - 3}
              </Badge>
            )}
          </div>
        )}
      </CardHeader>

      <CardContent className="pt-0">
        {/* Test Info */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="flex items-center space-x-2">
            <Badge className={getDifficultyColor(test.difficulty)}>
              {test.difficulty}
            </Badge>
          </div>
          
          <div className="flex items-center space-x-1 text-sm text-muted-foreground">
            <Clock className="w-4 h-4" />
            <span>{formatDuration(test.estimatedDuration)}</span>
          </div>
        </div>

        {/* Stats */}
        {showStats && (
          <div className="grid grid-cols-3 gap-2 mb-4 text-center">
            <div className="text-sm">
              <div className="font-medium text-mystical-600">
                {test.questionCount}
              </div>
              <div className="text-xs text-muted-foreground">Questions</div>
            </div>
            
            <div className="text-sm">
              <div className="font-medium text-mystical-600">
                {formatCount(test.completionCount)}
              </div>
              <div className="text-xs text-muted-foreground">Completed</div>
            </div>
            
            {test.views && (
              <div className="text-sm">
                <div className="font-medium text-mystical-600">
                  {formatCount(test.views)}
                </div>
                <div className="text-xs text-muted-foreground">Views</div>
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-2">
          <Button
            onClick={handleStartTest}
            className="flex-1"
            variant={test.featured ? 'default' : 'outline'}
          >
            <Play className="w-4 h-4 mr-2" />
            Start Test
          </Button>
          
          <Link href={`/tests/${test.slug}`}>
            <Button variant="ghost" size="sm">
              <Eye className="w-4 h-4" />
            </Button>
          </Link>
        </div>

        {/* Popularity Indicator */}
        {test.completionCount > 1000 && (
          <div className="flex items-center justify-center mt-3 text-xs text-muted-foreground">
            <TrendingUp className="w-3 h-3 mr-1" />
            Popular choice
          </div>
        )}
      </CardContent>
    </Card>
  );
}
