import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { Decimal } from '@prisma/client/runtime/library';

// 创建商品的验证模式
const createProductSchema = z.object({
  name: z.record(z.string().min(1)),
  description: z.record(z.string().min(1)),
  slug: z.string().min(1),
  price: z.number().positive(),
  compareAtPrice: z.number().positive().optional(),
  sku: z.string().min(1),
  inventory: z.number().int().min(0),
  trackInventory: z.boolean().default(true),
  categoryId: z.string(),
  tags: z.array(z.string()).default([]),
  images: z.array(z.object({
    url: z.string().url(),
    alt: z.string(),
    position: z.number().int().min(0),
  })).default([]),
  seoTitle: z.record(z.string()).optional(),
  seoDescription: z.record(z.string()).optional(),
  seoKeywords: z.record(z.array(z.string())).optional(),
  isActive: z.boolean().default(true),
  isFeatured: z.boolean().default(false),
  attributes: z.record(z.any()).optional(),
  variants: z.array(z.object({
    name: z.string(),
    sku: z.string(),
    price: z.number().positive().optional(),
    inventory: z.number().int().min(0),
    attributes: z.record(z.string()),
    isActive: z.boolean().default(true),
  })).optional(),
});

// 查询参数验证模式
const querySchema = z.object({
  page: z.string().transform(Number).default('1'),
  limit: z.string().transform(Number).default('20'),
  category: z.string().optional(),
  featured: z.string().transform(Boolean).optional(),
  active: z.string().transform(Boolean).optional(),
  search: z.string().optional(),
  sortBy: z.enum(['name', 'price', 'createdAt', 'inventory']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  minPrice: z.string().transform(Number).optional(),
  maxPrice: z.string().transform(Number).optional(),
  inStock: z.string().transform(Boolean).optional(),
});

// GET /api/products - 获取商品列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = querySchema.parse(Object.fromEntries(searchParams));

    const {
      page,
      limit,
      category,
      featured,
      active,
      search,
      sortBy,
      sortOrder,
      minPrice,
      maxPrice,
      inStock,
    } = query;

    // 构建查询条件
    const where: any = {};

    if (active !== undefined) {
      where.isActive = active;
    }

    if (featured !== undefined) {
      where.isFeatured = featured;
    }

    if (category) {
      where.categoryId = category;
    }

    if (search) {
      where.OR = [
        {
          name: {
            path: ['en'],
            string_contains: search,
          },
        },
        {
          description: {
            path: ['en'],
            string_contains: search,
          },
        },
        {
          sku: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    if (minPrice !== undefined || maxPrice !== undefined) {
      where.price = {};
      if (minPrice !== undefined) {
        where.price.gte = new Decimal(minPrice);
      }
      if (maxPrice !== undefined) {
        where.price.lte = new Decimal(maxPrice);
      }
    }

    if (inStock !== undefined) {
      if (inStock) {
        where.inventory = { gt: 0 };
      } else {
        where.inventory = { lte: 0 };
      }
    }

    // 计算分页
    const skip = (page - 1) * limit;

    // 构建排序
    const orderBy: any = {};
    if (sortBy === 'name') {
      orderBy.name = { path: ['en'], sort: sortOrder };
    } else {
      orderBy[sortBy] = sortOrder;
    }

    // 查询商品
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        skip,
        take: limit,
        orderBy,
        include: {
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          images: {
            orderBy: { position: 'asc' },
            take: 1,
          },
          variants: {
            select: {
              id: true,
              name: true,
              price: true,
              inventory: true,
              isActive: true,
            },
          },
          _count: {
            select: {
              orderItems: true,
            },
          },
        },
      }),
      prisma.product.count({ where }),
    ]);

    // 格式化响应数据
    const formattedProducts = products.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description,
      slug: product.slug,
      price: product.price.toNumber(),
      compareAtPrice: product.compareAtPrice?.toNumber(),
      sku: product.sku,
      inventory: product.inventory,
      trackInventory: product.trackInventory,
      isActive: product.isActive,
      isFeatured: product.isFeatured,
      category: product.category,
      coverImage: product.images[0]?.url,
      variantCount: product.variants.length,
      salesCount: product._count.orderItems,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
    }));

    // 计算分页信息
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return NextResponse.json({
      products: formattedProducts,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      },
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}

// POST /api/products - 创建新商品
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const data = createProductSchema.parse(body);

    // 检查slug是否已存在
    const existingProduct = await prisma.product.findUnique({
      where: { slug: data.slug },
    });

    if (existingProduct) {
      return NextResponse.json(
        { error: 'Product with this slug already exists' },
        { status: 400 }
      );
    }

    // 检查SKU是否已存在
    const existingSku = await prisma.product.findUnique({
      where: { sku: data.sku },
    });

    if (existingSku) {
      return NextResponse.json(
        { error: 'Product with this SKU already exists' },
        { status: 400 }
      );
    }

    // 创建商品
    const product = await prisma.product.create({
      data: {
        name: data.name,
        description: data.description,
        slug: data.slug,
        price: new Decimal(data.price),
        compareAtPrice: data.compareAtPrice ? new Decimal(data.compareAtPrice) : null,
        sku: data.sku,
        inventory: data.inventory,
        trackInventory: data.trackInventory,
        categoryId: data.categoryId,
        tags: data.tags,
        seoTitle: data.seoTitle,
        seoDescription: data.seoDescription,
        seoKeywords: data.seoKeywords,
        isActive: data.isActive,
        isFeatured: data.isFeatured,
        attributes: data.attributes,
        images: {
          create: data.images.map(image => ({
            url: image.url,
            alt: image.alt,
            position: image.position,
          })),
        },
        variants: data.variants ? {
          create: data.variants.map(variant => ({
            name: variant.name,
            sku: variant.sku,
            price: variant.price ? new Decimal(variant.price) : null,
            inventory: variant.inventory,
            attributes: variant.attributes,
            isActive: variant.isActive,
          })),
        } : undefined,
      },
      include: {
        category: true,
        images: {
          orderBy: { position: 'asc' },
        },
        variants: true,
      },
    });

    return NextResponse.json(product, { status: 201 });
  } catch (error) {
    console.error('Error creating product:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create product' },
      { status: 500 }
    );
  }
}
