{"name": "mystical-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "prepare": "husky", "test": "echo \"No tests specified\" && exit 0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,mdx,css,html,yml,yaml}": ["prettier --write"]}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@next/third-parties": "^14.2.5", "@prisma/client": "^5.17.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.2", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^11.3.8", "lucide-react": "^0.408.0", "next": "14.2.5", "next-intl": "^3.17.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.52.1", "redis": "^4.6.14", "sharp": "^0.33.4", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8", "zustand": "^4.5.4"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@sentry/nextjs": "^8.20.0", "@types/node": "^20.14.12", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.17.0", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^9.1.7", "lint-staged": "^15.5.2", "postcss": "^8.4.40", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.5", "prisma": "^5.17.0", "tailwindcss": "^3.4.7", "tsx": "^4.16.2", "typescript": "^5.5.4"}, "engines": {"node": ">=18.17.0"}}