'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button, Input, Textarea, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/atoms';
import { FormField } from '@/components/molecules';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/atoms';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/atoms';
import { Badge } from '@/components/atoms';
import { Save, Eye, Send, Clock, Image as ImageIcon } from 'lucide-react';
import { ArticleStatus } from '@prisma/client';
import { Locale } from '@/types';

// 表单验证模式
const articleFormSchema = z.object({
  title: z.record(z.string().min(1, 'Title is required')),
  content: z.record(z.string().min(1, 'Content is required')),
  excerpt: z.record(z.string()).optional(),
  slug: z.string().min(1, 'Slug is required'),
  coverImage: z.string().optional(),
  categoryId: z.string().min(1, 'Category is required'),
  tags: z.array(z.string()).default([]),
  status: z.nativeEnum(ArticleStatus),
  publishedAt: z.string().optional(),
  scheduledAt: z.string().optional(),
  seoTitle: z.record(z.string()).optional(),
  seoDescription: z.record(z.string()).optional(),
  seoKeywords: z.record(z.array(z.string())).optional(),
  featured: z.boolean().default(false),
});

type ArticleFormData = z.infer<typeof articleFormSchema>;

interface ArticleEditorProps {
  article?: Partial<ArticleFormData>;
  categories: Array<{ id: string; name: Record<string, string>; slug: string }>;
  availableTags: Array<{ id: string; name: Record<string, string>; slug: string }>;
  locales: Locale[];
  defaultLocale: Locale;
  onSave: (data: ArticleFormData) => Promise<void>;
  onPreview: (data: ArticleFormData) => void;
  isLoading?: boolean;
}

export function ArticleEditor({
  article,
  categories,
  availableTags,
  locales,
  defaultLocale,
  onSave,
  onPreview,
  isLoading = false,
}: ArticleEditorProps) {
  const [activeLocale, setActiveLocale] = useState<Locale>(defaultLocale);
  const [selectedTags, setSelectedTags] = useState<string[]>(article?.tags || []);
  const [wordCount, setWordCount] = useState(0);

  const form = useForm<ArticleFormData>({
    resolver: zodResolver(articleFormSchema),
    defaultValues: {
      title: article?.title || { [defaultLocale]: '' },
      content: article?.content || { [defaultLocale]: '' },
      excerpt: article?.excerpt || { [defaultLocale]: '' },
      slug: article?.slug || '',
      coverImage: article?.coverImage || '',
      categoryId: article?.categoryId || '',
      tags: article?.tags || [],
      status: article?.status || ArticleStatus.DRAFT,
      featured: article?.featured || false,
      seoTitle: article?.seoTitle || { [defaultLocale]: '' },
      seoDescription: article?.seoDescription || { [defaultLocale]: '' },
      seoKeywords: article?.seoKeywords || { [defaultLocale]: [] },
    },
  });

  const { register, handleSubmit, watch, setValue, formState: { errors } } = form;
  const watchedContent = watch(`content.${activeLocale}`);
  const watchedTitle = watch(`title.${activeLocale}`);

  // 计算字数
  useEffect(() => {
    if (watchedContent) {
      const words = watchedContent.trim().split(/\s+/).length;
      setWordCount(words);
    }
  }, [watchedContent]);

  // 自动生成slug
  useEffect(() => {
    if (watchedTitle && !article?.slug) {
      const slug = watchedTitle
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      setValue('slug', slug);
    }
  }, [watchedTitle, setValue, article?.slug]);

  const handleSave = async (data: ArticleFormData) => {
    try {
      await onSave({ ...data, tags: selectedTags });
    } catch (error) {
      console.error('Error saving article:', error);
    }
  };

  const handlePreview = () => {
    const data = form.getValues();
    onPreview({ ...data, tags: selectedTags });
  };

  const handleTagToggle = (tagId: string) => {
    setSelectedTags(prev => 
      prev.includes(tagId) 
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId]
    );
  };

  const getStatusColor = (status: ArticleStatus) => {
    switch (status) {
      case ArticleStatus.PUBLISHED:
        return 'bg-green-100 text-green-800';
      case ArticleStatus.DRAFT:
        return 'bg-gray-100 text-gray-800';
      case ArticleStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800';
      case ArticleStatus.SCHEDULED:
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">
            {article ? 'Edit Article' : 'Create New Article'}
          </h1>
          <p className="text-muted-foreground">
            {wordCount} words • {Math.ceil(wordCount / 200)} min read
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={handlePreview}
            disabled={isLoading}
          >
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </Button>
          
          <Button
            onClick={handleSubmit(handleSave)}
            disabled={isLoading}
          >
            <Save className="w-4 h-4 mr-2" />
            {isLoading ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </div>

      <form onSubmit={handleSubmit(handleSave)} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Language Tabs */}
            <Card>
              <CardHeader>
                <CardTitle>Content</CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs value={activeLocale} onValueChange={(value) => setActiveLocale(value as Locale)}>
                  <TabsList className="grid w-full grid-cols-6">
                    {locales.map((locale) => (
                      <TabsTrigger key={locale} value={locale}>
                        {locale.toUpperCase()}
                      </TabsTrigger>
                    ))}
                  </TabsList>
                  
                  {locales.map((locale) => (
                    <TabsContent key={locale} value={locale} className="space-y-4">
                      <FormField
                        label="Title"
                        required
                        error={errors.title?.[locale]?.message}
                        locale={locale}
                      >
                        <Input
                          {...register(`title.${locale}`)}
                          placeholder="Enter article title..."
                        />
                      </FormField>

                      <FormField
                        label="Content"
                        required
                        error={errors.content?.[locale]?.message}
                        locale={locale}
                      >
                        <Textarea
                          {...register(`content.${locale}`)}
                          placeholder="Write your article content..."
                          rows={20}
                          className="font-mono"
                        />
                      </FormField>

                      <FormField
                        label="Excerpt"
                        description="Brief summary of the article"
                        error={errors.excerpt?.[locale]?.message}
                        locale={locale}
                      >
                        <Textarea
                          {...register(`excerpt.${locale}`)}
                          placeholder="Enter article excerpt..."
                          rows={3}
                        />
                      </FormField>
                    </TabsContent>
                  ))}
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Publish Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Publish Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  label="Status"
                  error={errors.status?.message}
                >
                  <Select
                    value={watch('status')}
                    onValueChange={(value) => setValue('status', value as ArticleStatus)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(ArticleStatus).map((status) => (
                        <SelectItem key={status} value={status}>
                          <div className="flex items-center gap-2">
                            <Badge className={getStatusColor(status)}>
                              {status}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormField>

                <FormField
                  label="Category"
                  required
                  error={errors.categoryId?.message}
                >
                  <Select
                    value={watch('categoryId')}
                    onValueChange={(value) => setValue('categoryId', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name[activeLocale] || category.name[defaultLocale]}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormField>

                <FormField
                  label="Slug"
                  required
                  error={errors.slug?.message}
                >
                  <Input
                    {...register('slug')}
                    placeholder="article-slug"
                  />
                </FormField>
              </CardContent>
            </Card>

            {/* Tags */}
            <Card>
              <CardHeader>
                <CardTitle>Tags</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {availableTags.map((tag) => (
                    <Badge
                      key={tag.id}
                      variant={selectedTags.includes(tag.id) ? 'default' : 'outline'}
                      className="cursor-pointer"
                      onClick={() => handleTagToggle(tag.id)}
                    >
                      {tag.name[activeLocale] || tag.name[defaultLocale]}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Featured Image */}
            <Card>
              <CardHeader>
                <CardTitle>Featured Image</CardTitle>
              </CardHeader>
              <CardContent>
                <FormField
                  label="Image URL"
                  error={errors.coverImage?.message}
                >
                  <Input
                    {...register('coverImage')}
                    placeholder="https://example.com/image.jpg"
                  />
                </FormField>
                
                <Button
                  type="button"
                  variant="outline"
                  className="w-full mt-2"
                >
                  <ImageIcon className="w-4 h-4 mr-2" />
                  Upload Image
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  );
}
