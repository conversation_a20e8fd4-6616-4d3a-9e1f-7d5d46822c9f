import { forwardRef } from 'react';
import NextLink, { LinkProps as NextLinkProps } from 'next/link';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const linkVariants = cva(
  'inline-flex items-center transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'text-mystical-600 hover:text-mystical-800 dark:text-mystical-400 dark:hover:text-mystical-200',
        primary: 'text-mystical-600 hover:text-mystical-800 dark:text-mystical-400 dark:hover:text-mystical-200',
        secondary: 'text-muted-foreground hover:text-foreground',
        destructive: 'text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200',
        ghost: 'text-foreground hover:text-mystical-600 dark:hover:text-mystical-400',
        muted: 'text-muted-foreground hover:text-foreground',
        gold: 'text-gold-600 hover:text-gold-800 dark:text-gold-400 dark:hover:text-gold-200',
      },
      underline: {
        none: 'no-underline',
        hover: 'no-underline hover:underline',
        always: 'underline',
      },
      size: {
        sm: 'text-sm',
        md: 'text-base',
        lg: 'text-lg',
      },
      weight: {
        normal: 'font-normal',
        medium: 'font-medium',
        semibold: 'font-semibold',
        bold: 'font-bold',
      },
    },
    defaultVariants: {
      variant: 'default',
      underline: 'hover',
      size: 'md',
      weight: 'normal',
    },
  }
);

export interface LinkProps
  extends Omit<NextLinkProps, 'className'>,
    VariantProps<typeof linkVariants> {
  className?: string;
  children: React.ReactNode;
  external?: boolean;
  disabled?: boolean;
}

const Link = forwardRef<HTMLAnchorElement, LinkProps>(
  ({ 
    className, 
    variant, 
    underline, 
    size, 
    weight, 
    external = false,
    disabled = false,
    children,
    href,
    ...props 
  }, ref) => {
    // 如果是外部链接，使用普通的 a 标签
    if (external || (typeof href === 'string' && (href.startsWith('http') || href.startsWith('mailto:') || href.startsWith('tel:')))) {
      return (
        <a
          ref={ref}
          href={href as string}
          className={cn(
            linkVariants({ variant, underline, size, weight }),
            disabled && 'pointer-events-none opacity-50',
            className
          )}
          target={external ? '_blank' : undefined}
          rel={external ? 'noopener noreferrer' : undefined}
          aria-disabled={disabled}
          {...(props as any)}
        >
          {children}
        </a>
      );
    }

    // 内部链接使用 Next.js Link
    return (
      <NextLink
        ref={ref}
        href={href}
        className={cn(
          linkVariants({ variant, underline, size, weight }),
          disabled && 'pointer-events-none opacity-50',
          className
        )}
        aria-disabled={disabled}
        {...props}
      >
        {children}
      </NextLink>
    );
  }
);

Link.displayName = 'Link';

export { Link, linkVariants };
