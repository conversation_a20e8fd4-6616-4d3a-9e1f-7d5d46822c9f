# 组件架构文档

本项目采用原子设计系统（Atomic Design）和现代React模式构建组件架构。

## 组件分层结构

```
src/components/
├── atoms/           # 原子组件 - 最基础的UI元素
├── molecules/       # 分子组件 - 原子组件的组合
├── organisms/       # 有机体组件 - 复杂的功能模块
├── templates/       # 模板组件 - 页面级别的布局
├── patterns/        # 设计模式组件 - 可复用的模式
├── ui/             # 基础UI组件库 (shadcn/ui)
├── layout/         # 布局相关组件
├── seo/            # SEO相关组件
├── mobile/         # 移动端专用组件
└── README.md       # 本文档
```

## 设计原则

### 1. 原子设计系统
- **Atoms（原子）**: 最基础的UI元素，如Button、Input、Typography
- **Molecules（分子）**: 原子组件的简单组合，如SearchBox、FormField
- **Organisms（有机体）**: 复杂的功能模块，如Header、ProductGrid
- **Templates（模板）**: 页面级别的布局结构

### 2. 组件设计模式

#### 复合组件模式 (Compound Components)
```tsx
// 使用示例
<DataCard.Root variant="blog" locale="en">
  <DataCard.Header>
    <DataCard.Image src="..." alt="..." />
  </DataCard.Header>
  <DataCard.Body>
    <DataCard.Title>标题</DataCard.Title>
    <DataCard.Description>描述</DataCard.Description>
  </DataCard.Body>
  <DataCard.Footer>
    <DataCard.Actions>
      <Button>操作</Button>
    </DataCard.Actions>
  </DataCard.Footer>
</DataCard.Root>
```

#### 渲染属性模式 (Render Props)
```tsx
// 使用示例
<DataFetcher url="/api/posts">
  {({ data, loading, error, refetch }) => (
    loading ? <LoadingSpinner /> :
    error ? <ErrorMessage error={error} /> :
    <BlogList posts={data} />
  )}
</DataFetcher>
```

#### 高阶组件模式 (HOC)
```tsx
// 使用示例
const EnhancedComponent = compose(
  withSEO(defaultSEO),
  withI18n,
  withErrorBoundary,
  withLoading
)(MyComponent);
```

### 3. 多语言支持
所有组件都支持多语言，包括：
- 文字方向（LTR/RTL）
- 语言特定字体
- 文化敏感的颜色和符号
- 响应式字体大小调整

### 4. 移动端优化
- 触摸友好的交互设计
- 最小44px触摸目标
- 移动端专用组件
- 响应式布局适配

## 组件使用指南

### 基础组件
```tsx
import { Button, Typography, Image } from '@/components/atoms';

// 基础使用
<Button variant="primary" size="lg">点击我</Button>
<Typography variant="h1" locale="zh">标题</Typography>
<Image src="..." alt="..." aspectRatio="video" />
```

### 复合组件
```tsx
import { DataCard } from '@/components/molecules';

// 复合组件使用
<DataCard.Root variant="mystical">
  <DataCard.Body>
    <DataCard.Title>神秘卡片</DataCard.Title>
    <DataCard.Description>这是一个神秘学主题的卡片</DataCard.Description>
  </DataCard.Body>
</DataCard.Root>
```

### 数据获取
```tsx
import { DataFetcher } from '@/components/patterns';

// 数据获取组件
<DataFetcher url="/api/tarot-cards">
  {({ data, loading, error }) => (
    // 渲染逻辑
  )}
</DataFetcher>
```

### HOC增强
```tsx
import { withSEO, withI18n } from '@/components/patterns';

// HOC增强组件
const MyEnhancedPage = withSEO(
  withI18n(MyPage),
  {
    title: '页面标题',
    description: '页面描述',
  }
);
```

## 状态管理

使用Zustand进行模块化状态管理：

```tsx
import { useUIStore, useTheme, useNotifications } from '@/stores';

// 使用状态
const theme = useTheme();
const { add: addNotification } = useNotifications();
```

## 样式系统

### Tailwind CSS配置
- 神秘学主题颜色系统
- 多语言字体配置
- 移动端触摸目标
- RTL语言支持
- 神秘学主题动画

### 组件变体
使用class-variance-authority管理组件变体：

```tsx
const buttonVariants = cva(
  'base-classes',
  {
    variants: {
      variant: {
        primary: 'primary-classes',
        secondary: 'secondary-classes',
      },
      size: {
        sm: 'small-classes',
        lg: 'large-classes',
      },
    },
  }
);
```

## 最佳实践

1. **组件命名**: 使用PascalCase，描述性命名
2. **Props接口**: 继承基础接口，保持一致性
3. **错误处理**: 使用ErrorBoundary包装复杂组件
4. **性能优化**: 使用React.memo、useMemo、useCallback
5. **可访问性**: 遵循WCAG 2.1 AA标准
6. **测试**: 为每个组件编写单元测试

## 开发工作流

1. 设计组件接口
2. 实现基础功能
3. 添加变体和状态
4. 集成多语言支持
5. 优化移动端体验
6. 编写测试用例
7. 更新文档

## 贡献指南

1. 遵循现有的组件模式
2. 保持接口一致性
3. 添加适当的TypeScript类型
4. 编写清晰的文档
5. 确保多语言兼容性
6. 测试移动端体验
