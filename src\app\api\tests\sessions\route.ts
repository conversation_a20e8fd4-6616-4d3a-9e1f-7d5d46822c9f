import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { aiService } from '@/lib/ai/ai-service';
import { generateShareToken } from '@/lib/utils';

// 创建测试会话的验证模式
const createSessionSchema = z.object({
  testId: z.string(),
  userId: z.string().optional(),
  sessionId: z.string().optional(),
  locale: z.string().default('en'),
});

// 提交答案的验证模式
const submitAnswersSchema = z.object({
  sessionId: z.string(),
  answers: z.array(z.object({
    questionId: z.string(),
    answer: z.union([z.string(), z.array(z.string()), z.number()]),
  })),
});

// POST /api/tests/sessions - 创建新的测试会话
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const data = createSessionSchema.parse(body);

    // 检查测试是否存在且活跃
    const test = await prisma.mysticalTest.findUnique({
      where: { 
        id: data.testId,
        isActive: true,
      },
      include: {
        questions: {
          orderBy: { order: 'asc' },
        },
      },
    });

    if (!test) {
      return NextResponse.json(
        { error: 'Test not found or inactive' },
        { status: 404 }
      );
    }

    // 生成会话ID（如果未提供）
    const sessionId = data.sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 创建测试会话
    const session = await prisma.testSession.create({
      data: {
        testId: data.testId,
        userId: data.userId,
        sessionId,
        locale: data.locale,
        answers: {},
        status: 'STARTED',
        startedAt: new Date(),
      },
    });

    // 返回会话信息和第一个问题
    return NextResponse.json({
      sessionId: session.id,
      testInfo: {
        id: test.id,
        name: test.name,
        description: test.description,
        type: test.type,
        difficulty: test.difficulty,
        estimatedDuration: test.estimatedDuration,
        questionCount: test.questions.length,
      },
      currentQuestion: test.questions[0] ? {
        id: test.questions[0].id,
        text: test.questions[0].text,
        type: test.questions[0].type,
        options: test.questions[0].options,
        required: test.questions[0].required,
        order: test.questions[0].order,
      } : null,
      progress: {
        current: 0,
        total: test.questions.length,
        percentage: 0,
      },
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating test session:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create test session' },
      { status: 500 }
    );
  }
}

// PUT /api/tests/sessions - 提交答案并获取下一个问题或结果
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const data = submitAnswersSchema.parse(body);

    // 获取测试会话
    const session = await prisma.testSession.findUnique({
      where: { id: data.sessionId },
      include: {
        test: {
          include: {
            questions: {
              orderBy: { order: 'asc' },
            },
          },
        },
      },
    });

    if (!session) {
      return NextResponse.json(
        { error: 'Test session not found' },
        { status: 404 }
      );
    }

    if (session.status === 'COMPLETED') {
      return NextResponse.json(
        { error: 'Test session already completed' },
        { status: 400 }
      );
    }

    // 更新答案
    const currentAnswers = session.answers as any || {};
    data.answers.forEach(answer => {
      currentAnswers[answer.questionId] = {
        answer: answer.answer,
        timestamp: new Date().toISOString(),
      };
    });

    // 检查是否所有问题都已回答
    const totalQuestions = session.test.questions.length;
    const answeredQuestions = Object.keys(currentAnswers).length;
    const isCompleted = answeredQuestions >= totalQuestions;

    if (isCompleted) {
      // 测试完成，生成AI分析
      const result = await generateTestResult(session, currentAnswers);
      
      // 更新会话状态
      await prisma.testSession.update({
        where: { id: data.sessionId },
        data: {
          answers: currentAnswers,
          status: 'COMPLETED',
          completedAt: new Date(),
          result: result,
          shareToken: generateShareToken(),
        },
      });

      return NextResponse.json({
        completed: true,
        result,
        shareToken: generateShareToken(),
      });
    } else {
      // 获取下一个未回答的问题
      const nextQuestion = session.test.questions.find(q => 
        !currentAnswers[q.id]
      );

      // 更新会话
      await prisma.testSession.update({
        where: { id: data.sessionId },
        data: {
          answers: currentAnswers,
          status: 'IN_PROGRESS',
        },
      });

      return NextResponse.json({
        completed: false,
        nextQuestion: nextQuestion ? {
          id: nextQuestion.id,
          text: nextQuestion.text,
          type: nextQuestion.type,
          options: nextQuestion.options,
          required: nextQuestion.required,
          order: nextQuestion.order,
        } : null,
        progress: {
          current: answeredQuestions,
          total: totalQuestions,
          percentage: Math.round((answeredQuestions / totalQuestions) * 100),
        },
      });
    }

  } catch (error) {
    console.error('Error submitting answers:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to submit answers' },
      { status: 500 }
    );
  }
}

// 生成测试结果的辅助函数
async function generateTestResult(session: any, answers: any) {
  try {
    // 构建AI分析请求
    const prompt = buildAnalysisPrompt(session.test, answers, session.locale);
    
    // 调用AI服务
    const aiResponse = await aiService.generateText({
      prompt,
      context: `Test Type: ${session.test.type}, Locale: ${session.locale}`,
      type: 'general_mystical',
      locale: session.locale,
      userId: session.userId,
    });

    // 解析AI响应
    const analysisResult = parseAIResponse(aiResponse.content);

    return {
      summary: analysisResult.summary,
      details: analysisResult.details,
      recommendations: analysisResult.recommendations,
      aiInsights: aiResponse.content,
      shareableContent: {
        title: analysisResult.title,
        description: analysisResult.summary,
        image: session.test.coverImage,
      },
      metadata: {
        aiModel: aiResponse.model,
        aiProvider: aiResponse.provider,
        generatedAt: new Date().toISOString(),
        confidence: 0.85, // 可以根据实际情况调整
      },
    };

  } catch (error) {
    console.error('Error generating AI analysis:', error);
    
    // 返回默认结果
    return {
      summary: 'Thank you for completing the test. Your results are being processed.',
      details: [],
      recommendations: ['Continue exploring your spiritual journey.'],
      aiInsights: 'AI analysis temporarily unavailable.',
      shareableContent: {
        title: 'My Test Results',
        description: 'I just completed a mystical test!',
        image: session.test.coverImage,
      },
      metadata: {
        aiModel: 'fallback',
        aiProvider: 'system',
        generatedAt: new Date().toISOString(),
        confidence: 0.5,
      },
    };
  }
}

// 构建AI分析提示词
function buildAnalysisPrompt(test: any, answers: any, locale: string): string {
  const testType = test.type.toLowerCase();
  const answersText = Object.entries(answers).map(([questionId, answerData]: [string, any]) => {
    const question = test.questions.find((q: any) => q.id === questionId);
    return `Q: ${question?.text[locale] || question?.text.en || 'Unknown question'}
A: ${JSON.stringify(answerData.answer)}`;
  }).join('\n\n');

  return `As a professional ${testType} reader, please analyze the following test results and provide insights:

Test Type: ${test.type}
Test Name: ${test.name[locale] || test.name.en}

User's Answers:
${answersText}

Please provide a comprehensive analysis including:
1. Summary of key insights
2. Detailed interpretation
3. Practical recommendations
4. Future guidance

Respond in ${locale === 'zh' ? 'Chinese' : 'English'} with a warm, professional, and insightful tone.`;
}

// 解析AI响应
function parseAIResponse(content: string) {
  // 简化的解析逻辑，实际应用中可以更复杂
  const lines = content.split('\n').filter(line => line.trim());
  
  return {
    title: 'Your Mystical Analysis',
    summary: lines.slice(0, 2).join(' '),
    details: lines.slice(2, -2),
    recommendations: lines.slice(-2),
  };
}
