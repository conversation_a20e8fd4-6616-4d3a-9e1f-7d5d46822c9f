import { forwardRef } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';

const iconVariants = cva(
  'inline-flex items-center justify-center',
  {
    variants: {
      size: {
        xs: 'w-3 h-3',
        sm: 'w-4 h-4',
        md: 'w-5 h-5',
        lg: 'w-6 h-6',
        xl: 'w-8 h-8',
        '2xl': 'w-10 h-10',
        '3xl': 'w-12 h-12',
      },
      variant: {
        default: 'text-current',
        primary: 'text-mystical-600 dark:text-mystical-400',
        secondary: 'text-mystical-500 dark:text-mystical-500',
        success: 'text-green-600 dark:text-green-400',
        warning: 'text-yellow-600 dark:text-yellow-400',
        error: 'text-red-600 dark:text-red-400',
        muted: 'text-muted-foreground',
        gold: 'text-gold-600 dark:text-gold-400',
      },
      interactive: {
        true: 'cursor-pointer hover:opacity-80 transition-opacity',
        false: '',
      },
    },
    defaultVariants: {
      size: 'md',
      variant: 'default',
      interactive: false,
    },
  }
);

export interface IconProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof iconVariants> {
  icon: LucideIcon;
  'aria-label'?: string;
}

const Icon = forwardRef<HTMLDivElement, IconProps>(
  ({ className, size, variant, interactive, icon: IconComponent, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(iconVariants({ size, variant, interactive }), className)}
        {...props}
      >
        <IconComponent className="w-full h-full" />
      </div>
    );
  }
);

Icon.displayName = 'Icon';

export { Icon, iconVariants };
