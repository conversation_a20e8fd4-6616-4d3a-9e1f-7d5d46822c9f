'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { 
  Home, 
  BookOpen, 
  ShoppingBag, 
  TestTube, 
  User 
} from 'lucide-react';
import { Locale } from '@/types';
import { getMultilingualLayoutClasses } from '@/lib/multilingual-layout';
import { cn } from '@/lib/utils';

interface MobileBottomNavProps {
  locale: Locale;
}

export function MobileBottomNav({ locale }: MobileBottomNavProps) {
  const t = useTranslations('navigation');
  const pathname = usePathname();
  
  const layoutClasses = getMultilingualLayoutClasses(locale);
  
  const navigationItems = [
    {
      href: '/',
      label: t('home'),
      icon: Home,
    },
    {
      href: '/blog',
      label: t('blog'),
      icon: BookOpen,
    },
    {
      href: '/tests',
      label: t('tests'),
      icon: TestTube,
    },
    {
      href: '/products',
      label: t('products'),
      icon: ShoppingBag,
    },
    {
      href: '/about',
      label: t('about'),
      icon: User,
    },
  ];

  return (
    <nav className={`
      fixed bottom-0 left-0 right-0 z-30
      bg-white dark:bg-dark-800
      border-t border-mystical-200 dark:border-dark-700
      md:hidden
      ${layoutClasses}
    `}>
      <div className="flex items-center justify-around h-16 px-2">
        {navigationItems.map((item) => {
          const Icon = item.icon;
          const isActive = pathname === item.href || 
            (item.href !== '/' && pathname.startsWith(item.href));
          
          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                `
                  flex flex-col items-center justify-center
                  min-w-touch min-h-touch
                  px-2 py-1 rounded-lg
                  transition-colors duration-200
                `,
                isActive
                  ? 'text-mystical-600 dark:text-mystical-400 bg-mystical-50 dark:bg-mystical-900/20'
                  : 'text-mystical-500 dark:text-mystical-400 hover:text-mystical-600 dark:hover:text-mystical-300'
              )}
            >
              <Icon className="w-5 h-5 mb-1" />
              <span className="text-xs font-medium leading-none">
                {item.label}
              </span>
            </Link>
          );
        })}
      </div>
      
      {/* Safe area for devices with home indicator */}
      <div className="h-safe-area-inset-bottom bg-white dark:bg-dark-800" />
    </nav>
  );
}
