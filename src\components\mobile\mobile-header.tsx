'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { Menu, X, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { LanguageSwitcher } from '@/components/layout/language-switcher';
import { Locale } from '@/types';
import { getMultilingualLayoutClasses } from '@/lib/multilingual-layout';

interface MobileHeaderProps {
  locale: Locale;
}

export function MobileHeader({ locale }: MobileHeaderProps) {
  const t = useTranslations('navigation');
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  
  const layoutClasses = getMultilingualLayoutClasses(locale);
  
  const navigationItems = [
    { href: '/', label: t('home') },
    { href: '/blog', label: t('blog') },
    { href: '/products', label: t('products') },
    { href: '/tests', label: t('tests') },
    { href: '/tarot', label: t('tarot') },
    { href: '/astrology', label: t('astrology') },
    { href: '/numerology', label: t('numerology') },
    { href: '/about', label: t('about') },
    { href: '/contact', label: t('contact') },
  ];

  return (
    <header className={`
      sticky top-0 z-40 h-16 
      bg-white/95 dark:bg-dark-900/95 
      backdrop-blur-sm border-b border-mystical-200 dark:border-dark-700
      ${layoutClasses}
    `}>
      <div className="flex items-center justify-between h-full px-4">
        {/* Logo */}
        <Link 
          href="/" 
          className="flex items-center space-x-2"
        >
          <div className="w-8 h-8 bg-gradient-to-br from-mystical-500 to-gold-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-mystical text-sm">✦</span>
          </div>
          <span className="font-mystical text-lg font-bold text-mystical-900 dark:text-mystical-100 hidden sm:block">
            Mystical
          </span>
        </Link>

        {/* Desktop Navigation - Hidden on mobile */}
        <nav className="hidden md:flex items-center space-x-6">
          {navigationItems.slice(0, 5).map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className="text-mystical-700 dark:text-mystical-300 hover:text-mystical-900 dark:hover:text-mystical-100 transition-colors"
            >
              {item.label}
            </Link>
          ))}
        </nav>

        {/* Right side actions */}
        <div className="flex items-center space-x-2">
          {/* Search button - Hidden on small screens */}
          <Button
            variant="ghost"
            size="sm"
            className="hidden sm:flex min-w-touch min-h-touch"
          >
            <Search className="w-4 h-4" />
          </Button>

          {/* Language switcher */}
          <div className="hidden sm:block">
            <LanguageSwitcher />
          </div>

          {/* Mobile menu trigger */}
          <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="md:hidden min-w-touch min-h-touch"
              >
                <Menu className="w-5 h-5" />
                <span className="sr-only">Open menu</span>
              </Button>
            </SheetTrigger>
            
            <SheetContent 
              side={locale === 'ar' ? 'left' : 'right'} 
              className={`w-80 ${layoutClasses}`}
            >
              <div className="flex flex-col h-full">
                {/* Menu header */}
                <div className="flex items-center justify-between pb-4 border-b border-mystical-200 dark:border-dark-700">
                  <span className="font-mystical text-lg font-bold text-mystical-900 dark:text-mystical-100">
                    Menu
                  </span>
                </div>

                {/* Navigation items */}
                <nav className="flex-1 py-6">
                  <div className="space-y-1">
                    {navigationItems.map((item) => (
                      <Link
                        key={item.href}
                        href={item.href}
                        onClick={() => setIsMenuOpen(false)}
                        className="
                          flex items-center h-12 px-4 rounded-lg
                          text-mystical-700 dark:text-mystical-300
                          hover:bg-mystical-50 dark:hover:bg-dark-800
                          hover:text-mystical-900 dark:hover:text-mystical-100
                          transition-colors
                          min-h-touch
                        "
                      >
                        {item.label}
                      </Link>
                    ))}
                  </div>
                </nav>

                {/* Menu footer */}
                <div className="pt-4 border-t border-mystical-200 dark:border-dark-700 space-y-4">
                  {/* Search in menu */}
                  <Button
                    variant="outline"
                    className="w-full justify-start min-h-touch"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Search className="w-4 h-4 mr-2" />
                    Search
                  </Button>

                  {/* Language switcher in menu */}
                  <div className="flex justify-center">
                    <LanguageSwitcher />
                  </div>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}
