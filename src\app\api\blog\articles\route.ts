import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { ArticleStatus } from '@prisma/client';

// 创建文章的验证模式
const createArticleSchema = z.object({
  title: z.record(z.string()), // 多语言标题
  content: z.record(z.string()), // 多语言内容
  excerpt: z.record(z.string()).optional(), // 多语言摘要
  slug: z.string().min(1),
  coverImage: z.string().optional(),
  categoryId: z.string(),
  tags: z.array(z.string()).default([]),
  status: z.nativeEnum(ArticleStatus).default(ArticleStatus.DRAFT),
  publishedAt: z.string().datetime().optional(),
  scheduledAt: z.string().datetime().optional(),
  seoTitle: z.record(z.string()).optional(),
  seoDescription: z.record(z.string()).optional(),
  seoKeywords: z.record(z.array(z.string())).optional(),
  featured: z.boolean().default(false),
  aiGenerated: z.boolean().default(false),
  aiMetadata: z.record(z.any()).optional(),
});

// 查询参数验证模式
const querySchema = z.object({
  page: z.string().transform(Number).default('1'),
  limit: z.string().transform(Number).default('10'),
  status: z.nativeEnum(ArticleStatus).optional(),
  category: z.string().optional(),
  featured: z.string().transform(Boolean).optional(),
  search: z.string().optional(),
  locale: z.string().optional(),
  sortBy: z.enum(['createdAt', 'publishedAt', 'views', 'title']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// GET /api/blog/articles - 获取文章列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = querySchema.parse(Object.fromEntries(searchParams));

    const {
      page,
      limit,
      status,
      category,
      featured,
      search,
      locale,
      sortBy,
      sortOrder,
    } = query;

    // 构建查询条件
    const where: any = {};

    if (status) {
      where.status = status;
    }

    if (category) {
      where.categoryId = category;
    }

    if (featured !== undefined) {
      where.featured = featured;
    }

    if (search) {
      where.OR = [
        {
          title: {
            path: locale ? [locale] : undefined,
            string_contains: search,
          },
        },
        {
          content: {
            path: locale ? [locale] : undefined,
            string_contains: search,
          },
        },
      ];
    }

    // 计算分页
    const skip = (page - 1) * limit;

    // 查询文章
    const [articles, total] = await Promise.all([
      prisma.article.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          tags: {
            select: {
              id: true,
              name: true,
              slug: true,
              color: true,
            },
          },
          _count: {
            select: {
              comments: true,
            },
          },
        },
      }),
      prisma.article.count({ where }),
    ]);

    // 计算分页信息
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return NextResponse.json({
      articles,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      },
    });
  } catch (error) {
    console.error('Error fetching articles:', error);
    return NextResponse.json(
      { error: 'Failed to fetch articles' },
      { status: 500 }
    );
  }
}

// POST /api/blog/articles - 创建新文章
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const data = createArticleSchema.parse(body);

    // 检查slug是否已存在
    const existingArticle = await prisma.article.findUnique({
      where: { slug: data.slug },
    });

    if (existingArticle) {
      return NextResponse.json(
        { error: 'Article with this slug already exists' },
        { status: 400 }
      );
    }

    // 获取用户ID（这里需要从认证中获取）
    // const userId = await getCurrentUserId(request);
    const userId = 'temp-user-id'; // 临时处理

    // 创建文章
    const article = await prisma.article.create({
      data: {
        ...data,
        authorId: userId,
        publishedAt: data.status === ArticleStatus.PUBLISHED ? new Date() : data.publishedAt ? new Date(data.publishedAt) : null,
        scheduledAt: data.scheduledAt ? new Date(data.scheduledAt) : null,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        tags: {
          select: {
            id: true,
            name: true,
            slug: true,
            color: true,
          },
        },
      },
    });

    return NextResponse.json(article, { status: 201 });
  } catch (error) {
    console.error('Error creating article:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create article' },
      { status: 500 }
    );
  }
}
