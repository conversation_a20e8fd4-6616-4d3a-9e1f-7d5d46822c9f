'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from '@/components/atoms';
import { Button } from '@/components/atoms';
import { Typography } from '@/components/atoms';
import { Progress } from '@/components/atoms';
import { RadioGroup, RadioGroupItem } from '@/components/atoms';
import { Checkbox } from '@/components/atoms';
import { Input } from '@/components/atoms';
import { Textarea } from '@/components/atoms';
import { Slider } from '@/components/atoms';
import { 
  ArrowLeft, 
  ArrowRight, 
  Clock, 
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { Locale } from '@/types';
import { getMultilingualLayoutClasses } from '@/lib/multilingual-layout';

interface Question {
  id: string;
  text: Record<string, string>;
  type: 'SINGLE_CHOICE' | 'MULTIPLE_CHOICE' | 'TEXT' | 'SCALE' | 'DATE';
  options?: Record<string, string[]>;
  required: boolean;
  order: number;
}

interface TestInfo {
  id: string;
  name: Record<string, string>;
  description: Record<string, string>;
  type: string;
  difficulty: string;
  estimatedDuration: number;
  questionCount: number;
}

interface TestRunnerProps {
  sessionId: string;
  testInfo: TestInfo;
  initialQuestion: Question;
  locale: Locale;
  onComplete: (result: any) => void;
  onExit: () => void;
}

export function TestRunner({
  sessionId,
  testInfo,
  initialQuestion,
  locale,
  onComplete,
  onExit,
}: TestRunnerProps) {
  const [currentQuestion, setCurrentQuestion] = useState<Question>(initialQuestion);
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [currentAnswer, setCurrentAnswer] = useState<any>(null);
  const [progress, setProgress] = useState({ current: 0, total: testInfo.questionCount, percentage: 0 });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [errors, setErrors] = useState<string[]>([]);

  const layoutClasses = getMultilingualLayoutClasses(locale);

  // 计时器
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeElapsed(prev => prev + 1);
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // 格式化时间
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 验证当前答案
  const validateAnswer = () => {
    const newErrors: string[] = [];

    if (currentQuestion.required && (currentAnswer === null || currentAnswer === undefined || currentAnswer === '')) {
      newErrors.push('This question is required');
    }

    if (currentQuestion.type === 'MULTIPLE_CHOICE' && Array.isArray(currentAnswer) && currentAnswer.length === 0) {
      newErrors.push('Please select at least one option');
    }

    setErrors(newErrors);
    return newErrors.length === 0;
  };

  // 提交答案并获取下一个问题
  const submitAnswer = async () => {
    if (!validateAnswer()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/tests/sessions', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          answers: [{
            questionId: currentQuestion.id,
            answer: currentAnswer,
          }],
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit answer');
      }

      const result = await response.json();

      // 保存答案到本地状态
      setAnswers(prev => ({
        ...prev,
        [currentQuestion.id]: currentAnswer,
      }));

      if (result.completed) {
        // 测试完成
        onComplete(result.result);
      } else {
        // 继续下一个问题
        setCurrentQuestion(result.nextQuestion);
        setCurrentAnswer(null);
        setProgress(result.progress);
        setErrors([]);
      }
    } catch (error) {
      console.error('Error submitting answer:', error);
      setErrors(['Failed to submit answer. Please try again.']);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 渲染问题输入组件
  const renderQuestionInput = () => {
    const questionText = currentQuestion.text[locale] || currentQuestion.text.en;
    const options = currentQuestion.options?.[locale] || currentQuestion.options?.en || [];

    switch (currentQuestion.type) {
      case 'SINGLE_CHOICE':
        return (
          <RadioGroup
            value={currentAnswer || ''}
            onValueChange={setCurrentAnswer}
            className="space-y-3"
          >
            {options.map((option, index) => (
              <div key={index} className="flex items-center space-x-2">
                <RadioGroupItem value={option} id={`option-${index}`} />
                <label
                  htmlFor={`option-${index}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  {option}
                </label>
              </div>
            ))}
          </RadioGroup>
        );

      case 'MULTIPLE_CHOICE':
        return (
          <div className="space-y-3">
            {options.map((option, index) => (
              <div key={index} className="flex items-center space-x-2">
                <Checkbox
                  id={`option-${index}`}
                  checked={(currentAnswer || []).includes(option)}
                  onCheckedChange={(checked) => {
                    const newAnswer = currentAnswer || [];
                    if (checked) {
                      setCurrentAnswer([...newAnswer, option]);
                    } else {
                      setCurrentAnswer(newAnswer.filter((item: string) => item !== option));
                    }
                  }}
                />
                <label
                  htmlFor={`option-${index}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  {option}
                </label>
              </div>
            ))}
          </div>
        );

      case 'TEXT':
        return (
          <Textarea
            value={currentAnswer || ''}
            onChange={(e) => setCurrentAnswer(e.target.value)}
            placeholder="Enter your answer..."
            rows={4}
            className="w-full"
          />
        );

      case 'SCALE':
        return (
          <div className="space-y-4">
            <Slider
              value={[currentAnswer || 5]}
              onValueChange={(value) => setCurrentAnswer(value[0])}
              max={10}
              min={1}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>1 (Strongly Disagree)</span>
              <span className="font-medium">Current: {currentAnswer || 5}</span>
              <span>10 (Strongly Agree)</span>
            </div>
          </div>
        );

      case 'DATE':
        return (
          <Input
            type="date"
            value={currentAnswer || ''}
            onChange={(e) => setCurrentAnswer(e.target.value)}
            className="w-full"
          />
        );

      default:
        return (
          <Input
            value={currentAnswer || ''}
            onChange={(e) => setCurrentAnswer(e.target.value)}
            placeholder="Enter your answer..."
            className="w-full"
          />
        );
    }
  };

  return (
    <div className={`max-w-2xl mx-auto p-6 ${layoutClasses}`}>
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <Button
            variant="ghost"
            onClick={onExit}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Exit Test</span>
          </Button>
          
          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            <div className="flex items-center space-x-1">
              <Clock className="w-4 h-4" />
              <span>{formatTime(timeElapsed)}</span>
            </div>
            <div>
              Question {progress.current + 1} of {progress.total}
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <Progress value={progress.percentage} className="h-2" />
          <div className="text-center text-sm text-muted-foreground">
            {progress.percentage}% Complete
          </div>
        </div>
      </div>

      {/* Question Card */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-xl">
            <Typography variant="h3" locale={locale}>
              {currentQuestion.text[locale] || currentQuestion.text.en}
            </Typography>
          </CardTitle>
          {currentQuestion.required && (
            <div className="flex items-center space-x-1 text-sm text-red-600">
              <AlertCircle className="w-4 h-4" />
              <span>Required</span>
            </div>
          )}
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Question Input */}
          {renderQuestionInput()}

          {/* Errors */}
          {errors.length > 0 && (
            <div className="space-y-2">
              {errors.map((error, index) => (
                <div key={index} className="flex items-center space-x-2 text-red-600 text-sm">
                  <AlertCircle className="w-4 h-4" />
                  <span>{error}</span>
                </div>
              ))}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-between">
            <Button
              variant="outline"
              disabled={progress.current === 0}
              onClick={() => {
                // 实现返回上一题的逻辑
              }}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Previous
            </Button>

            <Button
              onClick={submitAnswer}
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Submitting...
                </>
              ) : progress.current === progress.total - 1 ? (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Complete Test
                </>
              ) : (
                <>
                  Next
                  <ArrowRight className="w-4 h-4 ml-2" />
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Test Info */}
      <Card>
        <CardContent className="p-4">
          <div className="text-center text-sm text-muted-foreground">
            <Typography variant="small" locale={locale}>
              {testInfo.name[locale] || testInfo.name.en}
            </Typography>
            <div className="mt-1">
              Estimated time: {testInfo.estimatedDuration} minutes
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
