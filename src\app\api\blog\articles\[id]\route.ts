import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { ArticleStatus } from '@prisma/client';

// 更新文章的验证模式
const updateArticleSchema = z.object({
  title: z.record(z.string()).optional(),
  content: z.record(z.string()).optional(),
  excerpt: z.record(z.string()).optional(),
  slug: z.string().optional(),
  coverImage: z.string().optional(),
  categoryId: z.string().optional(),
  tags: z.array(z.string()).optional(),
  status: z.nativeEnum(ArticleStatus).optional(),
  publishedAt: z.string().datetime().optional(),
  scheduledAt: z.string().datetime().optional(),
  seoTitle: z.record(z.string()).optional(),
  seoDescription: z.record(z.string()).optional(),
  seoKeywords: z.record(z.array(z.string())).optional(),
  featured: z.boolean().optional(),
});

// GET /api/blog/articles/[id] - 获取单篇文章
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const article = await prisma.article.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
            description: true,
          },
        },
        tags: {
          select: {
            id: true,
            name: true,
            slug: true,
            color: true,
          },
        },
        comments: {
          where: { approved: true },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
            replies: {
              include: {
                author: {
                  select: {
                    id: true,
                    name: true,
                    avatar: true,
                  },
                },
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
        revisions: {
          orderBy: { version: 'desc' },
          take: 5,
          include: {
            author: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        qualityChecks: {
          orderBy: { checkedAt: 'desc' },
          take: 1,
        },
        _count: {
          select: {
            comments: true,
            revisions: true,
          },
        },
      },
    });

    if (!article) {
      return NextResponse.json(
        { error: 'Article not found' },
        { status: 404 }
      );
    }

    // 增加浏览量
    await prisma.article.update({
      where: { id },
      data: { views: { increment: 1 } },
    });

    return NextResponse.json(article);
  } catch (error) {
    console.error('Error fetching article:', error);
    return NextResponse.json(
      { error: 'Failed to fetch article' },
      { status: 500 }
    );
  }
}

// PUT /api/blog/articles/[id] - 更新文章
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const data = updateArticleSchema.parse(body);

    // 检查文章是否存在
    const existingArticle = await prisma.article.findUnique({
      where: { id },
    });

    if (!existingArticle) {
      return NextResponse.json(
        { error: 'Article not found' },
        { status: 404 }
      );
    }

    // 如果更新了slug，检查是否冲突
    if (data.slug && data.slug !== existingArticle.slug) {
      const slugExists = await prisma.article.findUnique({
        where: { slug: data.slug },
      });

      if (slugExists) {
        return NextResponse.json(
          { error: 'Article with this slug already exists' },
          { status: 400 }
        );
      }
    }

    // 处理发布状态变更
    const updateData: any = { ...data };
    
    if (data.status === ArticleStatus.PUBLISHED && existingArticle.status !== ArticleStatus.PUBLISHED) {
      updateData.publishedAt = new Date();
    }

    if (data.scheduledAt) {
      updateData.scheduledAt = new Date(data.scheduledAt);
    }

    if (data.publishedAt) {
      updateData.publishedAt = new Date(data.publishedAt);
    }

    // 更新文章
    const article = await prisma.article.update({
      where: { id },
      data: updateData,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        tags: {
          select: {
            id: true,
            name: true,
            slug: true,
            color: true,
          },
        },
      },
    });

    return NextResponse.json(article);
  } catch (error) {
    console.error('Error updating article:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update article' },
      { status: 500 }
    );
  }
}

// DELETE /api/blog/articles/[id] - 删除文章（软删除）
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // 检查文章是否存在
    const existingArticle = await prisma.article.findUnique({
      where: { id },
    });

    if (!existingArticle) {
      return NextResponse.json(
        { error: 'Article not found' },
        { status: 404 }
      );
    }

    // 软删除：更新状态为DELETED
    await prisma.article.update({
      where: { id },
      data: { 
        status: ArticleStatus.DELETED,
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({ message: 'Article deleted successfully' });
  } catch (error) {
    console.error('Error deleting article:', error);
    return NextResponse.json(
      { error: 'Failed to delete article' },
      { status: 500 }
    );
  }
}
