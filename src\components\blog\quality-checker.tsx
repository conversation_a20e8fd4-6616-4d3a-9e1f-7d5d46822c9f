'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/atoms';
import { Progress } from '@/components/atoms';
import { Badge } from '@/components/atoms';
import { Button } from '@/components/atoms';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  RefreshCw,
  Search,
  Image as ImageIcon,
  Link as LinkIcon,
  FileText,
  Target
} from 'lucide-react';

interface QualityCheck {
  id: string;
  checkType: 'SEO' | 'READABILITY' | 'IMAGES' | 'LINKS' | 'DUPLICATES';
  score: number;
  issues: Array<{
    type: 'error' | 'warning' | 'info';
    message: string;
    suggestion?: string;
  }>;
  suggestions: Array<{
    title: string;
    description: string;
    priority: 'high' | 'medium' | 'low';
  }>;
  checkedAt: string;
}

interface QualityCheckerProps {
  articleId: string;
  content: Record<string, string>;
  title: Record<string, string>;
  locale: string;
  onRecheck?: () => void;
}

export function QualityChecker({
  articleId,
  content,
  title,
  locale,
  onRecheck,
}: QualityCheckerProps) {
  const [checks, setChecks] = useState<QualityCheck[]>([]);
  const [isChecking, setIsChecking] = useState(false);
  const [overallScore, setOverallScore] = useState(0);

  useEffect(() => {
    performQualityChecks();
  }, [articleId, content, title, locale]);

  const performQualityChecks = async () => {
    setIsChecking(true);
    
    try {
      // 模拟质量检查 - 在实际应用中这些会调用API
      const seoCheck = await checkSEO();
      const readabilityCheck = await checkReadability();
      const imageCheck = await checkImages();
      const linkCheck = await checkLinks();
      const duplicateCheck = await checkDuplicates();

      const allChecks = [seoCheck, readabilityCheck, imageCheck, linkCheck, duplicateCheck];
      setChecks(allChecks);

      // 计算总体评分
      const totalScore = allChecks.reduce((sum, check) => sum + check.score, 0) / allChecks.length;
      setOverallScore(Math.round(totalScore));

    } catch (error) {
      console.error('Quality check failed:', error);
    } finally {
      setIsChecking(false);
    }
  };

  const checkSEO = async (): Promise<QualityCheck> => {
    const articleTitle = title[locale] || '';
    const articleContent = content[locale] || '';
    
    const issues = [];
    const suggestions = [];
    let score = 100;

    // 标题长度检查
    if (articleTitle.length < 30) {
      issues.push({
        type: 'warning' as const,
        message: 'Title is too short (less than 30 characters)',
        suggestion: 'Consider expanding the title to 30-60 characters for better SEO',
      });
      score -= 15;
    } else if (articleTitle.length > 60) {
      issues.push({
        type: 'warning' as const,
        message: 'Title is too long (more than 60 characters)',
        suggestion: 'Shorten the title to under 60 characters to prevent truncation in search results',
      });
      score -= 10;
    }

    // 内容长度检查
    const wordCount = articleContent.split(/\s+/).length;
    if (wordCount < 300) {
      issues.push({
        type: 'error' as const,
        message: 'Content is too short (less than 300 words)',
        suggestion: 'Add more detailed content to improve SEO ranking',
      });
      score -= 25;
    }

    // 标题结构检查
    const headings = articleContent.match(/^#{1,6}\s+.+$/gm) || [];
    if (headings.length === 0) {
      issues.push({
        type: 'warning' as const,
        message: 'No headings found in content',
        suggestion: 'Add H2 and H3 headings to improve content structure',
      });
      score -= 15;
    }

    // 关键词密度检查（简化版）
    const titleWords = articleTitle.toLowerCase().split(/\s+/);
    const contentLower = articleContent.toLowerCase();
    const keywordDensity = titleWords.reduce((count, word) => {
      return count + (contentLower.split(word).length - 1);
    }, 0) / wordCount;

    if (keywordDensity < 0.01) {
      suggestions.push({
        title: 'Improve keyword usage',
        description: 'Include title keywords naturally throughout the content',
        priority: 'medium',
      });
    }

    return {
      id: 'seo-check',
      checkType: 'SEO',
      score: Math.max(0, score),
      issues,
      suggestions,
      checkedAt: new Date().toISOString(),
    };
  };

  const checkReadability = async (): Promise<QualityCheck> => {
    const articleContent = content[locale] || '';
    const issues = [];
    const suggestions = [];
    let score = 100;

    // 句子长度检查
    const sentences = articleContent.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const avgSentenceLength = sentences.reduce((sum, sentence) => {
      return sum + sentence.split(/\s+/).length;
    }, 0) / sentences.length;

    if (avgSentenceLength > 25) {
      issues.push({
        type: 'warning' as const,
        message: 'Average sentence length is too long',
        suggestion: 'Break down long sentences for better readability',
      });
      score -= 15;
    }

    // 段落长度检查
    const paragraphs = articleContent.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    const longParagraphs = paragraphs.filter(p => p.split(/\s+/).length > 150);
    
    if (longParagraphs.length > 0) {
      issues.push({
        type: 'info' as const,
        message: `${longParagraphs.length} paragraph(s) are very long`,
        suggestion: 'Consider breaking long paragraphs into smaller ones',
      });
      score -= 10;
    }

    // 被动语态检查（简化版）
    const passiveIndicators = ['was', 'were', 'been', 'being'];
    const passiveCount = passiveIndicators.reduce((count, indicator) => {
      return count + (articleContent.toLowerCase().split(indicator).length - 1);
    }, 0);

    if (passiveCount > sentences.length * 0.2) {
      suggestions.push({
        title: 'Reduce passive voice',
        description: 'Use more active voice to make content more engaging',
        priority: 'low',
      });
    }

    return {
      id: 'readability-check',
      checkType: 'READABILITY',
      score: Math.max(0, score),
      issues,
      suggestions,
      checkedAt: new Date().toISOString(),
    };
  };

  const checkImages = async (): Promise<QualityCheck> => {
    const articleContent = content[locale] || '';
    const issues = [];
    const suggestions = [];
    let score = 100;

    // 图片数量检查
    const images = articleContent.match(/!\[([^\]]*)\]\(([^)]+)\)/g) || [];
    const wordCount = articleContent.split(/\s+/).length;
    
    if (images.length === 0 && wordCount > 500) {
      issues.push({
        type: 'warning' as const,
        message: 'No images found in long content',
        suggestion: 'Add relevant images to break up text and improve engagement',
      });
      score -= 20;
    }

    // Alt文本检查
    const imagesWithoutAlt = images.filter(img => {
      const altMatch = img.match(/!\[([^\]]*)\]/);
      return !altMatch || !altMatch[1].trim();
    });

    if (imagesWithoutAlt.length > 0) {
      issues.push({
        type: 'error' as const,
        message: `${imagesWithoutAlt.length} image(s) missing alt text`,
        suggestion: 'Add descriptive alt text to all images for accessibility and SEO',
      });
      score -= 25;
    }

    return {
      id: 'images-check',
      checkType: 'IMAGES',
      score: Math.max(0, score),
      issues,
      suggestions,
      checkedAt: new Date().toISOString(),
    };
  };

  const checkLinks = async (): Promise<QualityCheck> => {
    const articleContent = content[locale] || '';
    const issues = [];
    const suggestions = [];
    let score = 100;

    // 链接检查
    const links = articleContent.match(/\[([^\]]+)\]\(([^)]+)\)/g) || [];
    const externalLinks = links.filter(link => {
      const urlMatch = link.match(/\]\(([^)]+)\)/);
      return urlMatch && (urlMatch[1].startsWith('http') || urlMatch[1].startsWith('https'));
    });

    // 内部链接建议
    if (links.length === 0) {
      suggestions.push({
        title: 'Add internal links',
        description: 'Link to related articles to improve SEO and user engagement',
        priority: 'medium',
      });
    }

    // 外部链接检查
    externalLinks.forEach(link => {
      const urlMatch = link.match(/\]\(([^)]+)\)/);
      if (urlMatch && !urlMatch[1].includes('rel="nofollow"')) {
        suggestions.push({
          title: 'Consider nofollow for external links',
          description: 'Add rel="nofollow" to external links to preserve link equity',
          priority: 'low',
        });
      }
    });

    return {
      id: 'links-check',
      checkType: 'LINKS',
      score: Math.max(0, score),
      issues,
      suggestions,
      checkedAt: new Date().toISOString(),
    };
  };

  const checkDuplicates = async (): Promise<QualityCheck> => {
    // 在实际应用中，这里会检查数据库中的重复内容
    const issues = [];
    const suggestions = [];
    let score = 100;

    // 模拟重复内容检查
    // 这里可以实现更复杂的重复检测算法

    return {
      id: 'duplicates-check',
      checkType: 'DUPLICATES',
      score,
      issues,
      suggestions,
      checkedAt: new Date().toISOString(),
    };
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 60) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  const getCheckIcon = (checkType: QualityCheck['checkType']) => {
    switch (checkType) {
      case 'SEO':
        return <Search className="w-5 h-5" />;
      case 'READABILITY':
        return <FileText className="w-5 h-5" />;
      case 'IMAGES':
        return <ImageIcon className="w-5 h-5" />;
      case 'LINKS':
        return <LinkIcon className="w-5 h-5" />;
      case 'DUPLICATES':
        return <Target className="w-5 h-5" />;
      default:
        return <CheckCircle className="w-5 h-5" />;
    }
  };

  const getIssueIcon = (type: 'error' | 'warning' | 'info') => {
    switch (type) {
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'info':
        return <CheckCircle className="w-4 h-4 text-blue-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Overall Score */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Content Quality Score</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={performQualityChecks}
            disabled={isChecking}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isChecking ? 'animate-spin' : ''}`} />
            {isChecking ? 'Checking...' : 'Recheck'}
          </Button>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className={`text-4xl font-bold ${getScoreColor(overallScore)}`}>
              {overallScore}%
            </div>
            <div className="flex-1">
              <Progress value={overallScore} className="h-3" />
              <p className="text-sm text-muted-foreground mt-1">
                Overall content quality score
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Individual Checks */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {checks.map((check) => (
          <Card key={check.id}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {getCheckIcon(check.checkType)}
                  <span className="font-medium">{check.checkType}</span>
                </div>
                <Badge className={getScoreBgColor(check.score)}>
                  <span className={getScoreColor(check.score)}>
                    {check.score}%
                  </span>
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              {/* Issues */}
              {check.issues.length > 0 && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Issues</h4>
                  {check.issues.map((issue, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      {getIssueIcon(issue.type)}
                      <div className="text-sm">
                        <p>{issue.message}</p>
                        {issue.suggestion && (
                          <p className="text-muted-foreground mt-1">
                            {issue.suggestion}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Suggestions */}
              {check.suggestions.length > 0 && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Suggestions</h4>
                  {check.suggestions.map((suggestion, index) => (
                    <div key={index} className="text-sm">
                      <p className="font-medium">{suggestion.title}</p>
                      <p className="text-muted-foreground">
                        {suggestion.description}
                      </p>
                    </div>
                  ))}
                </div>
              )}

              {check.issues.length === 0 && check.suggestions.length === 0 && (
                <div className="flex items-center space-x-2 text-green-600">
                  <CheckCircle className="w-4 h-4" />
                  <span className="text-sm">All checks passed</span>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
